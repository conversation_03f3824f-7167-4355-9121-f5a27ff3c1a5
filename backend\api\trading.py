"""
Trading and order management API endpoints
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, date
from decimal import Decimal
import uuid

from fastapi import APIRouter, Depends, HTTPException, Query, status, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from pydantic import BaseModel, Field

from app.database import get_db
from app.models.trade import Trade, TradeOrder, TradeExecution, OrderSide, OrderType, OrderStatus, TradeStatus
from app.models.bond import Bond
from app.models.portfolio import Portfolio, PortfolioHolding
from app.models.user import User
from api.auth import get_current_user, require_role, UserRole
from services.similarity_engine import SimilarityEngine

router = APIRouter()


# Pydantic models
class TradeCreate(BaseModel):
    """Trade creation request"""
    portfolio_id: str
    bond_cusip: str
    side: str  # buy or sell
    quantity: Decimal = Field(..., gt=0)
    order_type: str = "market"
    limit_price: Optional[Decimal] = None
    stop_price: Optional[Decimal] = None
    notes: Optional[str] = None


class TradeResponse(BaseModel):
    """Trade response model"""
    id: str
    trade_id: str
    portfolio_id: str
    bond_cusip: str
    bond_issuer: str
    side: str
    quantity: Decimal
    price: Optional[Decimal]
    gross_amount: Optional[Decimal]
    net_amount: Optional[Decimal]
    status: str
    trade_date: datetime
    settlement_date: Optional[datetime]
    yield_to_maturity: Optional[Decimal]
    is_alternative_trade: bool
    similarity_score: Optional[Decimal]
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime


class OrderResponse(BaseModel):
    """Order response model"""
    id: str
    order_id: str
    trade_id: str
    order_type: str
    side: str
    quantity: Decimal
    price: Optional[Decimal]
    stop_price: Optional[Decimal]
    status: str
    filled_quantity: Decimal
    remaining_quantity: Optional[Decimal]
    average_fill_price: Optional[Decimal]
    order_time: datetime
    expiry_time: Optional[datetime]


class AlternativeTradeRequest(BaseModel):
    """Alternative trade recommendation request"""
    original_bond_cusip: str
    portfolio_id: str
    side: str
    quantity: Decimal
    max_alternatives: int = Field(default=5, le=10)
    min_liquidity_improvement: float = Field(default=0.1, ge=0.0, le=1.0)


class AlternativeTradeResponse(BaseModel):
    """Alternative trade recommendation response"""
    original_bond: Dict[str, Any]
    alternatives: List[Dict[str, Any]]
    recommended_alternative: Optional[Dict[str, Any]]


class TradeExecutionResponse(BaseModel):
    """Trade execution response"""
    id: str
    execution_id: str
    trade_id: str
    quantity: Decimal
    price: Decimal
    gross_amount: Decimal
    execution_time: datetime
    execution_venue: Optional[str]
    counterparty: Optional[str]


@router.get("/", response_model=List[TradeResponse])
async def get_user_trades(
    portfolio_id: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    limit: int = Query(default=50, le=200),
    offset: int = Query(default=0, ge=0),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's trades with optional filters"""
    
    query = db.query(Trade).join(Portfolio).filter(
        Portfolio.user_id == current_user.id
    )
    
    if portfolio_id:
        query = query.filter(Trade.portfolio_id == portfolio_id)
    
    if status:
        query = query.filter(Trade.status == status)
    
    query = query.order_by(desc(Trade.trade_date)).offset(offset).limit(limit)
    trades = query.all()
    
    response_trades = []
    for trade in trades:
        trade_dict = {
            'id': str(trade.id),
            'trade_id': trade.trade_id,
            'portfolio_id': str(trade.portfolio_id),
            'bond_cusip': trade.bond.cusip,
            'bond_issuer': trade.bond.issuer_name,
            'side': trade.side,
            'quantity': trade.quantity,
            'price': trade.price,
            'gross_amount': trade.gross_amount,
            'net_amount': trade.net_amount,
            'status': trade.status,
            'trade_date': trade.trade_date,
            'settlement_date': trade.settlement_date,
            'yield_to_maturity': trade.yield_to_maturity,
            'is_alternative_trade': trade.is_alternative_trade,
            'similarity_score': trade.similarity_score,
            'notes': trade.notes,
            'created_at': trade.created_at,
            'updated_at': trade.updated_at
        }
        response_trades.append(TradeResponse(**trade_dict))
    
    return response_trades


@router.post("/", response_model=TradeResponse)
async def create_trade(
    trade_data: TradeCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new trade"""
    
    # Check trading permissions
    if not current_user.can_trade:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to execute trades"
        )
    
    # Verify portfolio ownership
    portfolio = db.query(Portfolio).filter(
        and_(
            Portfolio.id == trade_data.portfolio_id,
            Portfolio.user_id == current_user.id
        )
    ).first()
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    # Find bond
    bond = db.query(Bond).filter(
        and_(Bond.cusip == trade_data.bond_cusip, Bond.is_active == True)
    ).first()
    
    if not bond:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Bond with CUSIP {trade_data.bond_cusip} not found"
        )
    
    # Validate order type and prices
    try:
        OrderType(trade_data.order_type)
        OrderSide(trade_data.side)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid order parameter: {e}"
        )
    
    if trade_data.order_type == "limit" and not trade_data.limit_price:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Limit price required for limit orders"
        )
    
    # Generate trade ID
    trade_id = f"TRD-{datetime.utcnow().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
    
    # Calculate trade amounts (simplified)
    price = trade_data.limit_price or bond.current_price or Decimal('100.0')
    gross_amount = (trade_data.quantity * price) / 100  # Price per $100 face value
    
    # Create trade record
    trade = Trade(
        user_id=current_user.id,
        portfolio_id=trade_data.portfolio_id,
        bond_id=bond.id,
        trade_id=trade_id,
        side=trade_data.side,
        quantity=trade_data.quantity,
        price=price,
        gross_amount=gross_amount,
        status=TradeStatus.PENDING.value,
        notes=trade_data.notes
    )
    
    # Calculate net amount
    trade.calculate_net_amount()
    
    db.add(trade)
    db.flush()  # Get trade ID
    
    # Create order
    order_id = f"ORD-{datetime.utcnow().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
    order = TradeOrder(
        trade_id=trade.id,
        order_id=order_id,
        order_type=trade_data.order_type,
        side=trade_data.side,
        quantity=trade_data.quantity,
        price=trade_data.limit_price,
        stop_price=trade_data.stop_price,
        remaining_quantity=trade_data.quantity
    )
    
    db.add(order)
    db.commit()
    db.refresh(trade)
    
    # Submit order for execution (background task)
    background_tasks.add_task(_process_order, order.id, db)
    
    trade_dict = {
        'id': str(trade.id),
        'trade_id': trade.trade_id,
        'portfolio_id': str(trade.portfolio_id),
        'bond_cusip': bond.cusip,
        'bond_issuer': bond.issuer_name,
        'side': trade.side,
        'quantity': trade.quantity,
        'price': trade.price,
        'gross_amount': trade.gross_amount,
        'net_amount': trade.net_amount,
        'status': trade.status,
        'trade_date': trade.trade_date,
        'settlement_date': trade.settlement_date,
        'yield_to_maturity': trade.yield_to_maturity,
        'is_alternative_trade': trade.is_alternative_trade,
        'similarity_score': trade.similarity_score,
        'notes': trade.notes,
        'created_at': trade.created_at,
        'updated_at': trade.updated_at
    }
    
    return TradeResponse(**trade_dict)


@router.get("/{trade_id}", response_model=TradeResponse)
async def get_trade(
    trade_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get trade by ID"""
    
    trade = db.query(Trade).join(Portfolio).filter(
        and_(
            Trade.id == trade_id,
            Portfolio.user_id == current_user.id
        )
    ).first()
    
    if not trade:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Trade not found"
        )
    
    trade_dict = {
        'id': str(trade.id),
        'trade_id': trade.trade_id,
        'portfolio_id': str(trade.portfolio_id),
        'bond_cusip': trade.bond.cusip,
        'bond_issuer': trade.bond.issuer_name,
        'side': trade.side,
        'quantity': trade.quantity,
        'price': trade.price,
        'gross_amount': trade.gross_amount,
        'net_amount': trade.net_amount,
        'status': trade.status,
        'trade_date': trade.trade_date,
        'settlement_date': trade.settlement_date,
        'yield_to_maturity': trade.yield_to_maturity,
        'is_alternative_trade': trade.is_alternative_trade,
        'similarity_score': trade.similarity_score,
        'notes': trade.notes,
        'created_at': trade.created_at,
        'updated_at': trade.updated_at
    }
    
    return TradeResponse(**trade_dict)


@router.get("/{trade_id}/orders", response_model=List[OrderResponse])
async def get_trade_orders(
    trade_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get orders for a trade"""
    
    # Verify trade ownership
    trade = db.query(Trade).join(Portfolio).filter(
        and_(
            Trade.id == trade_id,
            Portfolio.user_id == current_user.id
        )
    ).first()
    
    if not trade:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Trade not found"
        )
    
    orders = db.query(TradeOrder).filter(
        TradeOrder.trade_id == trade_id
    ).order_by(TradeOrder.order_time.desc()).all()
    
    response_orders = []
    for order in orders:
        order_dict = {
            'id': str(order.id),
            'order_id': order.order_id,
            'trade_id': str(order.trade_id),
            'order_type': order.order_type,
            'side': order.side,
            'quantity': order.quantity,
            'price': order.price,
            'stop_price': order.stop_price,
            'status': order.status,
            'filled_quantity': order.filled_quantity,
            'remaining_quantity': order.remaining_quantity,
            'average_fill_price': order.average_fill_price,
            'order_time': order.order_time,
            'expiry_time': order.expiry_time
        }
        response_orders.append(OrderResponse(**order_dict))
    
    return response_orders


@router.get("/{trade_id}/executions", response_model=List[TradeExecutionResponse])
async def get_trade_executions(
    trade_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get executions for a trade"""

    # Verify trade ownership
    trade = db.query(Trade).join(Portfolio).filter(
        and_(
            Trade.id == trade_id,
            Portfolio.user_id == current_user.id
        )
    ).first()

    if not trade:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Trade not found"
        )

    executions = db.query(TradeExecution).filter(
        TradeExecution.trade_id == trade_id
    ).order_by(TradeExecution.execution_time.desc()).all()

    response_executions = []
    for execution in executions:
        execution_dict = {
            'id': str(execution.id),
            'execution_id': execution.execution_id,
            'trade_id': str(execution.trade_id),
            'quantity': execution.quantity,
            'price': execution.price,
            'gross_amount': execution.gross_amount,
            'execution_time': execution.execution_time,
            'execution_venue': execution.execution_venue,
            'counterparty': execution.counterparty
        }
        response_executions.append(TradeExecutionResponse(**execution_dict))

    return response_executions


@router.post("/alternatives", response_model=AlternativeTradeResponse)
async def get_alternative_trades(
    request: AlternativeTradeRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get alternative bond recommendations for trading"""

    # Verify portfolio ownership
    portfolio = db.query(Portfolio).filter(
        and_(
            Portfolio.id == request.portfolio_id,
            Portfolio.user_id == current_user.id
        )
    ).first()

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )

    # Find original bond
    original_bond = db.query(Bond).filter(
        and_(Bond.cusip == request.original_bond_cusip, Bond.is_active == True)
    ).first()

    if not original_bond:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Bond with CUSIP {request.original_bond_cusip} not found"
        )

    # Get candidate bonds (more liquid than original)
    candidates_query = db.query(Bond).filter(
        and_(
            Bond.is_active == True,
            Bond.id != original_bond.id,
            Bond.liquidity_score > original_bond.liquidity_score + (request.min_liquidity_improvement * 100)
        )
    ).limit(100)  # Limit for performance

    candidate_bonds = candidates_query.all()

    if not candidate_bonds:
        return AlternativeTradeResponse(
            original_bond=_bond_to_dict(original_bond),
            alternatives=[],
            recommended_alternative=None
        )

    # Initialize similarity engine
    similarity_engine = SimilarityEngine()
    await similarity_engine.initialize()

    # Convert to dict format
    original_data = _bond_to_dict(original_bond)
    candidate_data = [_bond_to_dict(bond) for bond in candidate_bonds]

    # Find similar bonds
    similarity_results = await similarity_engine.find_similar_bonds(
        target_bond=original_data,
        candidate_bonds=candidate_data,
        top_k=request.max_alternatives,
        min_liquidity_improvement=request.min_liquidity_improvement
    )

    # Convert results to response format
    alternatives = []
    for result in similarity_results:
        similar_bond = next(
            bond for bond in candidate_bonds
            if str(bond.id) == result.similar_bond_id
        )

        alternative = _bond_to_dict(similar_bond)
        alternative.update({
            'similarity_score': result.similarity_score,
            'liquidity_improvement': result.liquidity_improvement,
            'risk_adjustment': result.risk_adjustment,
            'confidence_score': result.confidence_score,
            'feature_similarities': result.feature_similarities
        })
        alternatives.append(alternative)

    # Recommend best alternative (highest similarity with good liquidity improvement)
    recommended_alternative = None
    if alternatives:
        # Score alternatives based on similarity and liquidity improvement
        for alt in alternatives:
            alt['recommendation_score'] = (
                0.6 * alt['similarity_score'] +
                0.4 * alt['liquidity_improvement']
            )

        recommended_alternative = max(alternatives, key=lambda x: x['recommendation_score'])

    return AlternativeTradeResponse(
        original_bond=original_data,
        alternatives=alternatives,
        recommended_alternative=recommended_alternative
    )


@router.post("/cancel/{trade_id}")
async def cancel_trade(
    trade_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Cancel a pending trade"""

    # Verify trade ownership
    trade = db.query(Trade).join(Portfolio).filter(
        and_(
            Trade.id == trade_id,
            Portfolio.user_id == current_user.id
        )
    ).first()

    if not trade:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Trade not found"
        )

    # Check if trade can be cancelled
    if trade.status not in [TradeStatus.PENDING.value, TradeStatus.SUBMITTED.value]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot cancel trade with status: {trade.status}"
        )

    # Cancel trade and associated orders
    trade.status = TradeStatus.CANCELLED.value
    trade.updated_at = datetime.utcnow()

    # Cancel all pending orders
    for order in trade.orders:
        if order.status in [OrderStatus.PENDING.value, OrderStatus.SUBMITTED.value]:
            order.status = OrderStatus.CANCELLED.value
            order.last_update_time = datetime.utcnow()

    db.commit()

    return {"message": "Trade cancelled successfully"}


# Helper functions
def _bond_to_dict(bond: Bond) -> Dict[str, Any]:
    """Convert Bond model to dictionary"""
    return {
        'id': str(bond.id),
        'cusip': bond.cusip,
        'isin': bond.isin,
        'symbol': bond.symbol,
        'issuer_name': bond.issuer_name,
        'bond_type': bond.bond_type,
        'description': bond.description,
        'face_value': float(bond.face_value or 1000),
        'coupon_rate': float(bond.coupon_rate or 0),
        'maturity_date': bond.maturity_date.isoformat() if bond.maturity_date else None,
        'current_price': float(bond.current_price or 100),
        'yield_to_maturity': float(bond.yield_to_maturity or 0),
        'duration': float(bond.duration or 0),
        'convexity': float(bond.convexity or 0),
        'liquidity_score': float(bond.liquidity_score or 50),
        'liquidity_tier': bond.liquidity_tier,
        'avg_daily_volume': float(bond.avg_daily_volume or 0),
        'bid_ask_spread': float(bond.bid_ask_spread or 0),
        'credit_spread': float(bond.credit_spread or 0),
        'sector': bond.sector,
        'industry': bond.industry,
        'country': bond.country,
        'currency': bond.currency,
        'is_callable': bond.is_callable,
        'is_puttable': bond.is_puttable,
        'time_to_maturity': bond.time_to_maturity,
        'credit_rating': _get_latest_rating(bond)
    }


def _get_latest_rating(bond: Bond) -> str:
    """Get latest credit rating for bond"""
    if bond.ratings:
        latest_rating = max(bond.ratings, key=lambda r: r.rating_date)
        return latest_rating.rating
    return "NR"  # Not Rated


async def _process_order(order_id: str, db: Session):
    """Background task to process order execution (simplified)"""
    try:
        order = db.query(TradeOrder).filter(TradeOrder.id == order_id).first()
        if not order:
            return

        # Simulate order processing
        import asyncio
        await asyncio.sleep(2)  # Simulate processing time

        # Update order status
        order.status = OrderStatus.FILLED.value
        order.filled_quantity = order.quantity
        order.remaining_quantity = Decimal('0')
        order.average_fill_price = order.price or Decimal('100.0')
        order.last_update_time = datetime.utcnow()

        # Update trade status
        trade = order.trade
        trade.status = TradeStatus.EXECUTED.value
        trade.price = order.average_fill_price
        trade.settlement_date = datetime.utcnow() + timedelta(days=2)  # T+2 settlement
        trade.updated_at = datetime.utcnow()

        # Create execution record
        execution_id = f"EXE-{datetime.utcnow().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
        execution = TradeExecution(
            trade_id=trade.id,
            execution_id=execution_id,
            quantity=order.quantity,
            price=order.average_fill_price,
            gross_amount=trade.gross_amount,
            execution_venue="INTERNAL",
            counterparty="MARKET_MAKER"
        )

        db.add(execution)

        # Update portfolio holding if this is a buy order
        if trade.side == OrderSide.BUY.value:
            await _update_portfolio_holding_buy(trade, db)
        elif trade.side == OrderSide.SELL.value:
            await _update_portfolio_holding_sell(trade, db)

        db.commit()

    except Exception as e:
        db.rollback()
        # Log error in production
        print(f"Order processing failed: {e}")


async def _update_portfolio_holding_buy(trade: Trade, db: Session):
    """Update portfolio holding for buy trade"""
    # Check if holding already exists
    existing_holding = db.query(PortfolioHolding).filter(
        and_(
            PortfolioHolding.portfolio_id == trade.portfolio_id,
            PortfolioHolding.bond_id == trade.bond_id,
            PortfolioHolding.is_active == True
        )
    ).first()

    if existing_holding:
        # Update existing holding (average cost calculation)
        total_cost = (existing_holding.quantity * existing_holding.average_cost) + (trade.quantity * trade.price)
        total_quantity = existing_holding.quantity + trade.quantity

        existing_holding.average_cost = total_cost / total_quantity
        existing_holding.quantity = total_quantity
        existing_holding.calculate_book_value()
        existing_holding.updated_at = datetime.utcnow()
    else:
        # Create new holding
        holding = PortfolioHolding(
            portfolio_id=trade.portfolio_id,
            bond_id=trade.bond_id,
            quantity=trade.quantity,
            average_cost=trade.price
        )
        holding.calculate_book_value()
        db.add(holding)


async def _update_portfolio_holding_sell(trade: Trade, db: Session):
    """Update portfolio holding for sell trade"""
    holding = db.query(PortfolioHolding).filter(
        and_(
            PortfolioHolding.portfolio_id == trade.portfolio_id,
            PortfolioHolding.bond_id == trade.bond_id,
            PortfolioHolding.is_active == True
        )
    ).first()

    if holding:
        if holding.quantity >= trade.quantity:
            holding.quantity -= trade.quantity
            if holding.quantity == 0:
                holding.is_active = False
            holding.calculate_book_value()
            holding.updated_at = datetime.utcnow()
        else:
            # Insufficient quantity - this should be caught in pre-trade validation
            raise ValueError("Insufficient quantity to sell")
