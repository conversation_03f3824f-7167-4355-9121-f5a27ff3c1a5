"""
User and authentication models
"""

from datetime import datetime
from typing import Optional, List
from enum import Enum

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Index
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid
from passlib.context import CryptContext

from app.database import Base

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class UserRole(str, Enum):
    """User role enumeration"""
    ADMIN = "admin"
    PORTFOLIO_MANAGER = "portfolio_manager"
    TRADER = "trader"
    RISK_MANAGER = "risk_manager"
    ANALYST = "analyst"
    VIEWER = "viewer"


class UserStatus(str, Enum):
    """User status enumeration"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending"


class User(Base):
    """User authentication and basic information model"""
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Authentication
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(50), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    
    # Basic Information
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    
    # Role and Status
    role = Column(String(50), nullable=False, default=UserRole.VIEWER, index=True)
    status = Column(String(20), nullable=False, default=UserStatus.PENDING, index=True)
    
    # Security
    is_active = Column(Boolean, default=True, index=True)
    is_verified = Column(Boolean, default=False)
    failed_login_attempts = Column(Integer, default=0)
    last_login = Column(DateTime)
    password_changed_at = Column(DateTime, default=datetime.utcnow)
    
    # Two-Factor Authentication
    is_2fa_enabled = Column(Boolean, default=False)
    totp_secret = Column(String(32))  # Base32 encoded secret
    backup_codes = Column(JSONB)  # List of backup codes
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    profile = relationship("UserProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    portfolios = relationship("Portfolio", back_populates="user", cascade="all, delete-orphan")
    trades = relationship("Trade", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(username='{self.username}', email='{self.email}')>"
    
    def verify_password(self, password: str) -> bool:
        """Verify password against hash"""
        return pwd_context.verify(password, self.hashed_password)
    
    def set_password(self, password: str):
        """Set password hash"""
        self.hashed_password = pwd_context.hash(password)
        self.password_changed_at = datetime.utcnow()
    
    @property
    def full_name(self) -> str:
        """Get full name"""
        return f"{self.first_name} {self.last_name}"
    
    @property
    def is_admin(self) -> bool:
        """Check if user is admin"""
        return self.role == UserRole.ADMIN
    
    @property
    def can_trade(self) -> bool:
        """Check if user can execute trades"""
        return self.role in [UserRole.ADMIN, UserRole.PORTFOLIO_MANAGER, UserRole.TRADER]
    
    @property
    def can_manage_portfolio(self) -> bool:
        """Check if user can manage portfolios"""
        return self.role in [UserRole.ADMIN, UserRole.PORTFOLIO_MANAGER]


class UserProfile(Base):
    """Extended user profile information"""
    __tablename__ = "user_profiles"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, unique=True)
    
    # Professional Information
    company = Column(String(255))
    department = Column(String(100))
    job_title = Column(String(100))
    
    # Contact Information
    phone = Column(String(20))
    mobile = Column(String(20))
    address = Column(Text)
    city = Column(String(100))
    state = Column(String(50))
    country = Column(String(3))  # ISO country code
    postal_code = Column(String(20))
    
    # Professional Details
    years_experience = Column(Integer)
    certifications = Column(JSONB)  # List of professional certifications
    specializations = Column(JSONB)  # Areas of expertise
    
    # Preferences
    timezone = Column(String(50), default="UTC")
    language = Column(String(5), default="en")
    currency_preference = Column(String(3), default="USD")
    
    # Notification Preferences
    email_notifications = Column(Boolean, default=True)
    sms_notifications = Column(Boolean, default=False)
    push_notifications = Column(Boolean, default=True)
    
    # Dashboard Preferences
    dashboard_layout = Column(JSONB)  # Custom dashboard configuration
    favorite_bonds = Column(JSONB)  # List of favorite bond CUSIPs
    watchlist = Column(JSONB)  # User's watchlist
    
    # Risk Preferences
    risk_tolerance = Column(String(20))  # Conservative, Moderate, Aggressive
    investment_objectives = Column(JSONB)  # List of investment goals
    
    # API Access
    api_key = Column(String(64), unique=True, index=True)
    api_key_created_at = Column(DateTime)
    api_rate_limit = Column(Integer, default=1000)  # Requests per hour
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="profile")
    
    def __repr__(self):
        return f"<UserProfile(user_id='{self.user_id}', company='{self.company}')>"
    
    def generate_api_key(self) -> str:
        """Generate new API key"""
        import secrets
        self.api_key = secrets.token_urlsafe(48)
        self.api_key_created_at = datetime.utcnow()
        return self.api_key
    
    @property
    def display_name(self) -> str:
        """Get display name for user"""
        if self.user:
            return self.user.full_name
        return "Unknown User"
