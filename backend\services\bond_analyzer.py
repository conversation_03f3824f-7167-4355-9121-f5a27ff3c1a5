"""
Bond Analysis Service
Comprehensive bond analysis including liquidity assessment, risk metrics, and performance analytics
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta, date
import asyncio
import logging
from decimal import Decimal

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.database import get_db
from app.models.bond import Bond, BondPrice, BondRating, LiquidityTier
from app.models.portfolio import Portfolio, PortfolioHolding
from .liquidity_scorer import LiquidityScorer
from .market_data import MarketDataService

logger = logging.getLogger(__name__)


@dataclass
class BondAnalysisResult:
    """Comprehensive bond analysis result"""
    bond_id: str
    cusip: str
    issuer_name: str
    
    # Liquidity Analysis
    liquidity_score: float
    liquidity_tier: str
    liquidity_risk: str
    
    # Risk Metrics
    credit_risk_score: float
    interest_rate_risk: float
    duration_risk: float
    concentration_risk: float
    
    # Performance Metrics
    total_return_1m: Optional[float]
    total_return_3m: Optional[float]
    total_return_ytd: Optional[float]
    volatility: Optional[float]
    
    # Valuation
    fair_value_estimate: Optional[float]
    price_deviation: Optional[float]
    
    # Recommendations
    recommendation: str  # BUY, HOLD, SELL, AVOID
    confidence_level: float
    key_risks: List[str]
    opportunities: List[str]


class BondAnalyzer:
    """Main bond analysis engine"""
    
    def __init__(self):
        self.liquidity_scorer = LiquidityScorer()
        self.market_data_service = MarketDataService()
        self.is_initialized = False
        
        # Risk thresholds
        self.risk_thresholds = {
            'high_duration_risk': 7.0,
            'high_credit_spread': 500,  # basis points
            'low_liquidity_score': 30,
            'high_concentration': 10.0  # percentage
        }
        
        # Performance benchmarks
        self.benchmarks = {
            'investment_grade': 'AGG',  # iShares Core US Aggregate Bond ETF
            'high_yield': 'HYG',        # iShares iBoxx High Yield Corporate Bond ETF
            'treasury': 'IEF'           # iShares 7-10 Year Treasury Bond ETF
        }
    
    async def initialize(self):
        """Initialize the bond analyzer"""
        try:
            logger.info("Initializing Bond Analyzer...")
            
            await self.liquidity_scorer.initialize()
            await self.market_data_service.initialize()
            
            self.is_initialized = True
            logger.info("✅ Bond Analyzer initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Bond Analyzer: {e}")
            raise
    
    async def analyze_bond(self, bond_id: str, db: Session) -> BondAnalysisResult:
        """Perform comprehensive analysis of a single bond"""
        if not self.is_initialized:
            await self.initialize()
        
        # Get bond data
        bond = db.query(Bond).filter(Bond.id == bond_id).first()
        if not bond:
            raise ValueError(f"Bond with ID {bond_id} not found")
        
        # Liquidity analysis
        liquidity_analysis = await self._analyze_liquidity(bond, db)
        
        # Risk analysis
        risk_analysis = await self._analyze_risks(bond, db)
        
        # Performance analysis
        performance_analysis = await self._analyze_performance(bond, db)
        
        # Valuation analysis
        valuation_analysis = await self._analyze_valuation(bond, db)
        
        # Generate recommendation
        recommendation = self._generate_recommendation(
            bond, liquidity_analysis, risk_analysis, performance_analysis, valuation_analysis
        )
        
        return BondAnalysisResult(
            bond_id=str(bond.id),
            cusip=bond.cusip,
            issuer_name=bond.issuer_name,
            
            # Liquidity
            liquidity_score=liquidity_analysis['score'],
            liquidity_tier=liquidity_analysis['tier'],
            liquidity_risk=liquidity_analysis['risk_level'],
            
            # Risk
            credit_risk_score=risk_analysis['credit_risk'],
            interest_rate_risk=risk_analysis['interest_rate_risk'],
            duration_risk=risk_analysis['duration_risk'],
            concentration_risk=risk_analysis['concentration_risk'],
            
            # Performance
            total_return_1m=performance_analysis.get('return_1m'),
            total_return_3m=performance_analysis.get('return_3m'),
            total_return_ytd=performance_analysis.get('return_ytd'),
            volatility=performance_analysis.get('volatility'),
            
            # Valuation
            fair_value_estimate=valuation_analysis.get('fair_value'),
            price_deviation=valuation_analysis.get('price_deviation'),
            
            # Recommendation
            recommendation=recommendation['action'],
            confidence_level=recommendation['confidence'],
            key_risks=recommendation['risks'],
            opportunities=recommendation['opportunities']
        )
    
    async def _analyze_liquidity(self, bond: Bond, db: Session) -> Dict[str, Any]:
        """Analyze bond liquidity"""
        # Get liquidity score from liquidity scorer
        liquidity_score = await self.liquidity_scorer.calculate_liquidity_score(
            str(bond.id), db
        )
        
        # Determine liquidity tier
        if liquidity_score >= 80:
            tier = LiquidityTier.TIER_1
            risk_level = "LOW"
        elif liquidity_score >= 60:
            tier = LiquidityTier.TIER_2
            risk_level = "MEDIUM"
        elif liquidity_score >= 40:
            tier = LiquidityTier.TIER_3
            risk_level = "HIGH"
        else:
            tier = LiquidityTier.TIER_4
            risk_level = "VERY_HIGH"
        
        return {
            'score': liquidity_score,
            'tier': tier.value,
            'risk_level': risk_level
        }
    
    async def _analyze_risks(self, bond: Bond, db: Session) -> Dict[str, float]:
        """Analyze various risk factors"""
        risks = {}
        
        # Credit risk based on rating and spread
        latest_rating = self._get_latest_rating(bond)
        if latest_rating:
            credit_risk = self._calculate_credit_risk(latest_rating.rating, bond.credit_spread or 0)
        else:
            credit_risk = 0.5  # Default moderate risk
        risks['credit_risk'] = credit_risk
        
        # Interest rate risk (duration-based)
        duration = bond.duration or 0
        interest_rate_risk = min(duration / 10, 1.0)  # Normalize to 0-1
        risks['interest_rate_risk'] = interest_rate_risk
        
        # Duration risk
        duration_risk = 1.0 if duration > self.risk_thresholds['high_duration_risk'] else duration / 10
        risks['duration_risk'] = duration_risk
        
        # Concentration risk (sector/issuer)
        concentration_risk = await self._calculate_concentration_risk(bond, db)
        risks['concentration_risk'] = concentration_risk
        
        return risks
    
    def _get_latest_rating(self, bond: Bond) -> Optional[BondRating]:
        """Get the most recent rating for a bond"""
        if bond.ratings:
            return max(bond.ratings, key=lambda r: r.rating_date)
        return None
    
    def _calculate_credit_risk(self, rating: str, credit_spread: float) -> float:
        """Calculate credit risk score based on rating and spread"""
        # Rating component (0-1, higher is riskier)
        rating_risk = {
            'AAA': 0.05, 'AA+': 0.1, 'AA': 0.1, 'AA-': 0.15,
            'A+': 0.2, 'A': 0.25, 'A-': 0.3,
            'BBB+': 0.4, 'BBB': 0.5, 'BBB-': 0.6,
            'BB+': 0.7, 'BB': 0.75, 'BB-': 0.8,
            'B+': 0.85, 'B': 0.9, 'B-': 0.95,
            'CCC': 0.98, 'CC': 0.99, 'C': 1.0, 'D': 1.0
        }.get(rating.upper(), 0.5)
        
        # Spread component (normalized)
        spread_risk = min(credit_spread / 1000, 1.0)  # Normalize to 1000 bps max
        
        # Combined risk (weighted average)
        return 0.7 * rating_risk + 0.3 * spread_risk
    
    async def _calculate_concentration_risk(self, bond: Bond, db: Session) -> float:
        """Calculate concentration risk for issuer/sector"""
        # This is a simplified calculation
        # In practice, you'd analyze portfolio-wide concentration
        
        # Count bonds from same issuer
        same_issuer_count = db.query(Bond).filter(
            Bond.issuer_name == bond.issuer_name,
            Bond.is_active == True
        ).count()
        
        # Count bonds in same sector
        same_sector_count = db.query(Bond).filter(
            Bond.sector == bond.sector,
            Bond.is_active == True
        ).count()
        
        # Simple concentration risk calculation
        issuer_concentration = min(same_issuer_count / 100, 1.0)
        sector_concentration = min(same_sector_count / 500, 1.0)
        
        return max(issuer_concentration, sector_concentration)

    async def _analyze_performance(self, bond: Bond, db: Session) -> Dict[str, Optional[float]]:
        """Analyze bond performance metrics"""
        performance = {}

        # Get price history
        end_date = datetime.utcnow().date()

        # 1-month return
        start_date_1m = end_date - timedelta(days=30)
        return_1m = await self._calculate_return(bond, start_date_1m, end_date, db)
        performance['return_1m'] = return_1m

        # 3-month return
        start_date_3m = end_date - timedelta(days=90)
        return_3m = await self._calculate_return(bond, start_date_3m, end_date, db)
        performance['return_3m'] = return_3m

        # Year-to-date return
        start_date_ytd = date(end_date.year, 1, 1)
        return_ytd = await self._calculate_return(bond, start_date_ytd, end_date, db)
        performance['return_ytd'] = return_ytd

        # Volatility (30-day)
        volatility = await self._calculate_volatility(bond, 30, db)
        performance['volatility'] = volatility

        return performance

    async def _calculate_return(self, bond: Bond, start_date: date, end_date: date, db: Session) -> Optional[float]:
        """Calculate total return for a period"""
        # Get prices at start and end of period
        start_price = db.query(BondPrice).filter(
            BondPrice.bond_id == bond.id,
            BondPrice.price_date >= start_date
        ).order_by(BondPrice.price_date.asc()).first()

        end_price = db.query(BondPrice).filter(
            BondPrice.bond_id == bond.id,
            BondPrice.price_date <= end_date
        ).order_by(BondPrice.price_date.desc()).first()

        if not start_price or not end_price:
            return None

        # Calculate price return
        price_return = (float(end_price.price) - float(start_price.price)) / float(start_price.price)

        # Add accrued interest (simplified)
        days = (end_date - start_date).days
        coupon_rate = float(bond.coupon_rate or 0) / 100
        accrued_interest = (coupon_rate * days) / 365

        total_return = price_return + accrued_interest
        return total_return * 100  # Return as percentage

    async def _calculate_volatility(self, bond: Bond, days: int, db: Session) -> Optional[float]:
        """Calculate price volatility"""
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=days)

        prices = db.query(BondPrice).filter(
            BondPrice.bond_id == bond.id,
            BondPrice.price_date >= start_date,
            BondPrice.price_date <= end_date
        ).order_by(BondPrice.price_date.asc()).all()

        if len(prices) < 5:  # Need minimum data points
            return None

        # Calculate daily returns
        returns = []
        for i in range(1, len(prices)):
            prev_price = float(prices[i-1].price)
            curr_price = float(prices[i].price)
            daily_return = (curr_price - prev_price) / prev_price
            returns.append(daily_return)

        if not returns:
            return None

        # Calculate volatility (standard deviation of returns)
        volatility = np.std(returns) * np.sqrt(252)  # Annualized
        return volatility * 100  # Return as percentage

    async def _analyze_valuation(self, bond: Bond, db: Session) -> Dict[str, Optional[float]]:
        """Analyze bond valuation"""
        valuation = {}

        # Simple fair value estimation based on yield curve
        fair_value = await self._estimate_fair_value(bond)
        valuation['fair_value'] = fair_value

        # Price deviation from fair value
        if fair_value and bond.current_price:
            current_price = float(bond.current_price)
            price_deviation = ((current_price - fair_value) / fair_value) * 100
            valuation['price_deviation'] = price_deviation
        else:
            valuation['price_deviation'] = None

        return valuation

    async def _estimate_fair_value(self, bond: Bond) -> Optional[float]:
        """Estimate fair value using simplified DCF model"""
        if not all([bond.coupon_rate, bond.maturity_date, bond.yield_to_maturity]):
            return None

        try:
            coupon_rate = float(bond.coupon_rate) / 100
            ytm = float(bond.yield_to_maturity) / 100
            face_value = float(bond.face_value or 1000)

            # Calculate time to maturity in years
            time_to_maturity = (bond.maturity_date - date.today()).days / 365.25

            if time_to_maturity <= 0:
                return face_value  # Bond has matured

            # Present value of coupon payments
            coupon_payment = face_value * coupon_rate
            pv_coupons = 0

            for year in range(1, int(time_to_maturity) + 1):
                pv_coupons += coupon_payment / ((1 + ytm) ** year)

            # Present value of principal
            pv_principal = face_value / ((1 + ytm) ** time_to_maturity)

            fair_value = pv_coupons + pv_principal
            return fair_value

        except Exception as e:
            logger.warning(f"Fair value calculation failed for bond {bond.cusip}: {e}")
            return None

    def _generate_recommendation(self, bond: Bond, liquidity_analysis: Dict,
                               risk_analysis: Dict, performance_analysis: Dict,
                               valuation_analysis: Dict) -> Dict[str, Any]:
        """Generate investment recommendation"""

        risks = []
        opportunities = []
        score = 0.5  # Neutral starting point

        # Liquidity assessment
        liquidity_score = liquidity_analysis['score']
        if liquidity_score < 30:
            risks.append("Very low liquidity - difficult to trade")
            score -= 0.2
        elif liquidity_score < 50:
            risks.append("Low liquidity - may face trading challenges")
            score -= 0.1
        elif liquidity_score > 80:
            opportunities.append("High liquidity - easy to trade")
            score += 0.1

        # Credit risk assessment
        credit_risk = risk_analysis['credit_risk']
        if credit_risk > 0.8:
            risks.append("High credit risk - potential default concerns")
            score -= 0.3
        elif credit_risk > 0.6:
            risks.append("Elevated credit risk")
            score -= 0.1
        elif credit_risk < 0.3:
            opportunities.append("Strong credit quality")
            score += 0.1

        # Duration risk
        duration_risk = risk_analysis['duration_risk']
        if duration_risk > 0.7:
            risks.append("High duration risk - sensitive to interest rate changes")
            score -= 0.1

        # Valuation assessment
        price_deviation = valuation_analysis.get('price_deviation')
        if price_deviation:
            if price_deviation < -5:
                opportunities.append("Trading below fair value - potential upside")
                score += 0.2
            elif price_deviation > 5:
                risks.append("Trading above fair value - overvalued")
                score -= 0.2

        # Performance assessment
        return_3m = performance_analysis.get('return_3m')
        if return_3m:
            if return_3m > 2:
                opportunities.append("Strong recent performance")
                score += 0.1
            elif return_3m < -2:
                risks.append("Poor recent performance")
                score -= 0.1

        # Generate recommendation
        if score >= 0.7:
            action = "BUY"
            confidence = min(score, 0.95)
        elif score >= 0.3:
            action = "HOLD"
            confidence = 0.6 + (score - 0.3) * 0.5
        elif score >= 0.1:
            action = "SELL"
            confidence = 0.5 + (0.3 - score) * 0.5
        else:
            action = "AVOID"
            confidence = min(1.0 - score, 0.95)

        return {
            'action': action,
            'confidence': confidence,
            'risks': risks,
            'opportunities': opportunities
        }

    async def analyze_portfolio_bonds(self, portfolio_id: str, db: Session) -> List[BondAnalysisResult]:
        """Analyze all bonds in a portfolio"""
        # Get portfolio holdings
        holdings = db.query(PortfolioHolding).filter(
            PortfolioHolding.portfolio_id == portfolio_id,
            PortfolioHolding.is_active == True
        ).all()

        results = []
        for holding in holdings:
            try:
                analysis = await self.analyze_bond(str(holding.bond_id), db)
                results.append(analysis)
            except Exception as e:
                logger.error(f"Failed to analyze bond {holding.bond_id}: {e}")
                continue

        return results

    async def screen_bonds(self, criteria: Dict[str, Any], db: Session) -> List[BondAnalysisResult]:
        """Screen bonds based on criteria"""
        # Build query based on criteria
        query = db.query(Bond).filter(Bond.is_active == True)

        # Apply filters
        if criteria.get('min_liquidity_score'):
            query = query.filter(Bond.liquidity_score >= criteria['min_liquidity_score'])

        if criteria.get('max_duration'):
            query = query.filter(Bond.duration <= criteria['max_duration'])

        if criteria.get('min_yield'):
            query = query.filter(Bond.yield_to_maturity >= criteria['min_yield'])

        if criteria.get('sectors'):
            query = query.filter(Bond.sector.in_(criteria['sectors']))

        if criteria.get('bond_types'):
            query = query.filter(Bond.bond_type.in_(criteria['bond_types']))

        bonds = query.limit(100).all()  # Limit results

        results = []
        for bond in bonds:
            try:
                analysis = await self.analyze_bond(str(bond.id), db)
                results.append(analysis)
            except Exception as e:
                logger.error(f"Failed to analyze bond {bond.id}: {e}")
                continue

        return results
