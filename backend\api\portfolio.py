"""
Portfolio management API endpoints
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, date
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from pydantic import BaseModel, Field

from app.database import get_db
from app.models.portfolio import Portfolio, PortfolioHolding, PortfolioType, PortfolioStatus
from app.models.bond import Bond
from app.models.user import User
from api.auth import get_current_user, require_role, UserRole
from services.bond_analyzer import BondAnalyzer
from services.liquidity_scorer import LiquidityScorer

router = APIRouter()


# Pydantic models
class PortfolioCreate(BaseModel):
    """Portfolio creation request"""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    portfolio_type: str
    base_currency: str = "USD"
    benchmark_name: Optional[str] = None
    max_position_size: Optional[Decimal] = Field(default=10.0, ge=0.1, le=100.0)
    min_credit_rating: Optional[str] = None
    max_duration: Optional[Decimal] = None


class PortfolioUpdate(BaseModel):
    """Portfolio update request"""
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None
    benchmark_name: Optional[str] = None
    max_position_size: Optional[Decimal] = None
    min_credit_rating: Optional[str] = None
    max_duration: Optional[Decimal] = None


class PortfolioResponse(BaseModel):
    """Portfolio response model"""
    id: str
    name: str
    description: Optional[str]
    portfolio_type: str
    status: str
    base_currency: str
    total_value: Decimal
    cash_balance: Decimal
    invested_amount: Decimal
    total_return: Optional[Decimal]
    ytd_return: Optional[Decimal]
    duration: Optional[Decimal]
    yield_to_maturity: Optional[Decimal]
    liquidity_score: Optional[Decimal]
    liquid_percentage: Optional[Decimal]
    illiquid_percentage: Optional[Decimal]
    number_of_holdings: int
    benchmark_name: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class HoldingCreate(BaseModel):
    """Portfolio holding creation request"""
    bond_cusip: str
    quantity: Decimal = Field(..., gt=0)
    average_cost: Decimal = Field(..., gt=0)


class HoldingUpdate(BaseModel):
    """Portfolio holding update request"""
    quantity: Optional[Decimal] = Field(None, gt=0)
    average_cost: Optional[Decimal] = Field(None, gt=0)


class HoldingResponse(BaseModel):
    """Portfolio holding response model"""
    id: str
    bond_cusip: str
    bond_issuer: str
    bond_description: Optional[str]
    quantity: Decimal
    average_cost: Decimal
    current_price: Optional[Decimal]
    book_value: Optional[Decimal]
    market_value: Optional[Decimal]
    unrealized_pnl: Optional[Decimal]
    weight: Optional[Decimal]
    duration_contribution: Optional[Decimal]
    yield_contribution: Optional[Decimal]
    liquidity_score: Optional[Decimal]
    liquidity_tier: Optional[str]
    is_active: bool
    created_at: datetime
    updated_at: datetime


class PortfolioAnalysis(BaseModel):
    """Portfolio analysis response"""
    portfolio_id: str
    total_bonds: int
    liquidity_analysis: Dict[str, Any]
    risk_metrics: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    sector_allocation: Dict[str, float]
    rating_distribution: Dict[str, float]
    maturity_distribution: Dict[str, float]
    recommendations: List[str]


@router.get("/", response_model=List[PortfolioResponse])
async def get_user_portfolios(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all portfolios for current user"""
    
    portfolios = db.query(Portfolio).filter(
        Portfolio.user_id == current_user.id
    ).all()
    
    response_portfolios = []
    for portfolio in portfolios:
        portfolio_dict = portfolio.__dict__.copy()
        portfolio_dict['number_of_holdings'] = len(portfolio.holdings)
        response_portfolios.append(PortfolioResponse(**portfolio_dict))
    
    return response_portfolios


@router.post("/", response_model=PortfolioResponse)
async def create_portfolio(
    portfolio_data: PortfolioCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new portfolio"""
    
    # Check if user can create portfolios
    if not current_user.can_manage_portfolio:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to create portfolios"
        )
    
    # Validate portfolio type
    try:
        PortfolioType(portfolio_data.portfolio_type)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid portfolio type"
        )
    
    # Create portfolio
    portfolio = Portfolio(
        user_id=current_user.id,
        name=portfolio_data.name,
        description=portfolio_data.description,
        portfolio_type=portfolio_data.portfolio_type,
        base_currency=portfolio_data.base_currency,
        benchmark_name=portfolio_data.benchmark_name,
        max_position_size=portfolio_data.max_position_size,
        min_credit_rating=portfolio_data.min_credit_rating,
        max_duration=portfolio_data.max_duration,
        status=PortfolioStatus.ACTIVE.value
    )
    
    db.add(portfolio)
    db.commit()
    db.refresh(portfolio)
    
    portfolio_dict = portfolio.__dict__.copy()
    portfolio_dict['number_of_holdings'] = 0
    
    return PortfolioResponse(**portfolio_dict)


@router.get("/{portfolio_id}", response_model=PortfolioResponse)
async def get_portfolio(
    portfolio_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get portfolio by ID"""
    
    portfolio = db.query(Portfolio).filter(
        and_(
            Portfolio.id == portfolio_id,
            Portfolio.user_id == current_user.id
        )
    ).first()
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    portfolio_dict = portfolio.__dict__.copy()
    portfolio_dict['number_of_holdings'] = len(portfolio.holdings)
    
    return PortfolioResponse(**portfolio_dict)


@router.put("/{portfolio_id}", response_model=PortfolioResponse)
async def update_portfolio(
    portfolio_id: str,
    portfolio_data: PortfolioUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update portfolio"""
    
    portfolio = db.query(Portfolio).filter(
        and_(
            Portfolio.id == portfolio_id,
            Portfolio.user_id == current_user.id
        )
    ).first()
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    # Update fields
    update_data = portfolio_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(portfolio, field, value)
    
    portfolio.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(portfolio)
    
    portfolio_dict = portfolio.__dict__.copy()
    portfolio_dict['number_of_holdings'] = len(portfolio.holdings)
    
    return PortfolioResponse(**portfolio_dict)


@router.delete("/{portfolio_id}")
async def delete_portfolio(
    portfolio_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete portfolio"""
    
    portfolio = db.query(Portfolio).filter(
        and_(
            Portfolio.id == portfolio_id,
            Portfolio.user_id == current_user.id
        )
    ).first()
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    # Check if portfolio has holdings
    if portfolio.holdings:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete portfolio with holdings"
        )
    
    db.delete(portfolio)
    db.commit()
    
    return {"message": "Portfolio deleted successfully"}


@router.get("/{portfolio_id}/holdings", response_model=List[HoldingResponse])
async def get_portfolio_holdings(
    portfolio_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all holdings in a portfolio"""
    
    # Verify portfolio ownership
    portfolio = db.query(Portfolio).filter(
        and_(
            Portfolio.id == portfolio_id,
            Portfolio.user_id == current_user.id
        )
    ).first()
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    holdings = db.query(PortfolioHolding).join(Bond).filter(
        and_(
            PortfolioHolding.portfolio_id == portfolio_id,
            PortfolioHolding.is_active == True
        )
    ).all()
    
    response_holdings = []
    for holding in holdings:
        holding_dict = {
            'id': str(holding.id),
            'bond_cusip': holding.bond.cusip,
            'bond_issuer': holding.bond.issuer_name,
            'bond_description': holding.bond.description,
            'quantity': holding.quantity,
            'average_cost': holding.average_cost,
            'current_price': holding.current_price,
            'book_value': holding.book_value,
            'market_value': holding.market_value,
            'unrealized_pnl': holding.unrealized_pnl,
            'weight': holding.weight,
            'duration_contribution': holding.duration_contribution,
            'yield_contribution': holding.yield_contribution,
            'liquidity_score': holding.bond.liquidity_score,
            'liquidity_tier': holding.bond.liquidity_tier,
            'is_active': holding.is_active,
            'created_at': holding.created_at,
            'updated_at': holding.updated_at
        }
        response_holdings.append(HoldingResponse(**holding_dict))
    
    return response_holdings


@router.post("/{portfolio_id}/holdings", response_model=HoldingResponse)
async def add_portfolio_holding(
    portfolio_id: str,
    holding_data: HoldingCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add a new holding to portfolio"""

    # Verify portfolio ownership
    portfolio = db.query(Portfolio).filter(
        and_(
            Portfolio.id == portfolio_id,
            Portfolio.user_id == current_user.id
        )
    ).first()

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )

    # Find bond by CUSIP
    bond = db.query(Bond).filter(
        and_(Bond.cusip == holding_data.bond_cusip, Bond.is_active == True)
    ).first()

    if not bond:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Bond with CUSIP {holding_data.bond_cusip} not found"
        )

    # Check if holding already exists
    existing_holding = db.query(PortfolioHolding).filter(
        and_(
            PortfolioHolding.portfolio_id == portfolio_id,
            PortfolioHolding.bond_id == bond.id,
            PortfolioHolding.is_active == True
        )
    ).first()

    if existing_holding:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Holding for this bond already exists in portfolio"
        )

    # Create new holding
    holding = PortfolioHolding(
        portfolio_id=portfolio_id,
        bond_id=bond.id,
        quantity=holding_data.quantity,
        average_cost=holding_data.average_cost
    )

    # Calculate book value
    holding.calculate_book_value()

    # Update market value if current price available
    if bond.current_price:
        holding.update_market_value(bond.current_price)

    db.add(holding)
    db.commit()
    db.refresh(holding)

    # Update portfolio metrics
    await _update_portfolio_metrics(portfolio, db)

    holding_dict = {
        'id': str(holding.id),
        'bond_cusip': bond.cusip,
        'bond_issuer': bond.issuer_name,
        'bond_description': bond.description,
        'quantity': holding.quantity,
        'average_cost': holding.average_cost,
        'current_price': holding.current_price,
        'book_value': holding.book_value,
        'market_value': holding.market_value,
        'unrealized_pnl': holding.unrealized_pnl,
        'weight': holding.weight,
        'duration_contribution': holding.duration_contribution,
        'yield_contribution': holding.yield_contribution,
        'liquidity_score': bond.liquidity_score,
        'liquidity_tier': bond.liquidity_tier,
        'is_active': holding.is_active,
        'created_at': holding.created_at,
        'updated_at': holding.updated_at
    }

    return HoldingResponse(**holding_dict)


@router.put("/{portfolio_id}/holdings/{holding_id}", response_model=HoldingResponse)
async def update_portfolio_holding(
    portfolio_id: str,
    holding_id: str,
    holding_data: HoldingUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a portfolio holding"""

    # Verify portfolio ownership and get holding
    holding = db.query(PortfolioHolding).join(Portfolio).filter(
        and_(
            PortfolioHolding.id == holding_id,
            PortfolioHolding.portfolio_id == portfolio_id,
            Portfolio.user_id == current_user.id,
            PortfolioHolding.is_active == True
        )
    ).first()

    if not holding:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Holding not found"
        )

    # Update fields
    update_data = holding_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(holding, field, value)

    # Recalculate values
    holding.calculate_book_value()
    if holding.bond.current_price:
        holding.update_market_value(holding.bond.current_price)

    holding.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(holding)

    # Update portfolio metrics
    portfolio = db.query(Portfolio).filter(Portfolio.id == portfolio_id).first()
    await _update_portfolio_metrics(portfolio, db)

    holding_dict = {
        'id': str(holding.id),
        'bond_cusip': holding.bond.cusip,
        'bond_issuer': holding.bond.issuer_name,
        'bond_description': holding.bond.description,
        'quantity': holding.quantity,
        'average_cost': holding.average_cost,
        'current_price': holding.current_price,
        'book_value': holding.book_value,
        'market_value': holding.market_value,
        'unrealized_pnl': holding.unrealized_pnl,
        'weight': holding.weight,
        'duration_contribution': holding.duration_contribution,
        'yield_contribution': holding.yield_contribution,
        'liquidity_score': holding.bond.liquidity_score,
        'liquidity_tier': holding.bond.liquidity_tier,
        'is_active': holding.is_active,
        'created_at': holding.created_at,
        'updated_at': holding.updated_at
    }

    return HoldingResponse(**holding_dict)


@router.delete("/{portfolio_id}/holdings/{holding_id}")
async def remove_portfolio_holding(
    portfolio_id: str,
    holding_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Remove a holding from portfolio"""

    # Verify portfolio ownership and get holding
    holding = db.query(PortfolioHolding).join(Portfolio).filter(
        and_(
            PortfolioHolding.id == holding_id,
            PortfolioHolding.portfolio_id == portfolio_id,
            Portfolio.user_id == current_user.id,
            PortfolioHolding.is_active == True
        )
    ).first()

    if not holding:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Holding not found"
        )

    # Soft delete
    holding.is_active = False
    holding.updated_at = datetime.utcnow()
    db.commit()

    # Update portfolio metrics
    portfolio = db.query(Portfolio).filter(Portfolio.id == portfolio_id).first()
    await _update_portfolio_metrics(portfolio, db)

    return {"message": "Holding removed successfully"}


@router.get("/{portfolio_id}/analysis", response_model=PortfolioAnalysis)
async def analyze_portfolio(
    portfolio_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get comprehensive portfolio analysis"""

    # Verify portfolio ownership
    portfolio = db.query(Portfolio).filter(
        and_(
            Portfolio.id == portfolio_id,
            Portfolio.user_id == current_user.id
        )
    ).first()

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )

    # Initialize analyzers
    bond_analyzer = BondAnalyzer()
    liquidity_scorer = LiquidityScorer()

    await bond_analyzer.initialize()
    await liquidity_scorer.initialize()

    # Get portfolio liquidity analysis
    liquidity_analysis = await liquidity_scorer.calculate_portfolio_liquidity(portfolio_id, db)

    # Analyze individual bonds
    bond_analyses = await bond_analyzer.analyze_portfolio_bonds(portfolio_id, db)

    # Calculate risk metrics
    risk_metrics = _calculate_portfolio_risk_metrics(portfolio, bond_analyses)

    # Calculate performance metrics
    performance_metrics = _calculate_portfolio_performance_metrics(portfolio)

    # Calculate allocations
    sector_allocation = _calculate_sector_allocation(portfolio.holdings)
    rating_distribution = _calculate_rating_distribution(portfolio.holdings)
    maturity_distribution = _calculate_maturity_distribution(portfolio.holdings)

    # Generate recommendations
    recommendations = _generate_portfolio_recommendations(
        portfolio, bond_analyses, liquidity_analysis, risk_metrics
    )

    return PortfolioAnalysis(
        portfolio_id=portfolio_id,
        total_bonds=len([h for h in portfolio.holdings if h.is_active]),
        liquidity_analysis=liquidity_analysis,
        risk_metrics=risk_metrics,
        performance_metrics=performance_metrics,
        sector_allocation=sector_allocation,
        rating_distribution=rating_distribution,
        maturity_distribution=maturity_distribution,
        recommendations=recommendations
    )


# Helper functions
async def _update_portfolio_metrics(portfolio: Portfolio, db: Session):
    """Update portfolio-level metrics"""

    # Calculate total value
    total_value = portfolio.calculate_total_value()
    portfolio.total_value = total_value

    # Calculate other metrics (simplified)
    active_holdings = [h for h in portfolio.holdings if h.is_active]

    if active_holdings:
        # Weighted average duration
        total_market_value = sum(float(h.market_value or 0) for h in active_holdings)
        if total_market_value > 0:
            weighted_duration = sum(
                float(h.bond.duration or 0) * float(h.market_value or 0) / total_market_value
                for h in active_holdings
            )
            portfolio.duration = Decimal(str(weighted_duration))

            # Weighted average yield
            weighted_yield = sum(
                float(h.bond.yield_to_maturity or 0) * float(h.market_value or 0) / total_market_value
                for h in active_holdings
            )
            portfolio.yield_to_maturity = Decimal(str(weighted_yield))

            # Weighted average liquidity score
            weighted_liquidity = sum(
                float(h.bond.liquidity_score or 50) * float(h.market_value or 0) / total_market_value
                for h in active_holdings
            )
            portfolio.liquidity_score = Decimal(str(weighted_liquidity))

    portfolio.updated_at = datetime.utcnow()
    db.commit()


def _calculate_portfolio_risk_metrics(portfolio: Portfolio, bond_analyses: List) -> Dict[str, Any]:
    """Calculate portfolio risk metrics"""
    if not bond_analyses:
        return {}

    # Average risk scores
    avg_credit_risk = sum(analysis.credit_risk_score for analysis in bond_analyses) / len(bond_analyses)
    avg_duration_risk = sum(analysis.duration_risk for analysis in bond_analyses) / len(bond_analyses)
    avg_liquidity_risk = sum(100 - analysis.liquidity_score for analysis in bond_analyses) / len(bond_analyses)

    return {
        'average_credit_risk': avg_credit_risk,
        'average_duration_risk': avg_duration_risk,
        'average_liquidity_risk': avg_liquidity_risk / 100,
        'concentration_risk': float(portfolio.top_10_concentration or 0) / 100
    }


def _calculate_portfolio_performance_metrics(portfolio: Portfolio) -> Dict[str, Any]:
    """Calculate portfolio performance metrics"""
    return {
        'total_return': float(portfolio.total_return or 0),
        'ytd_return': float(portfolio.ytd_return or 0),
        'tracking_error': float(portfolio.tracking_error or 0),
        'information_ratio': float(portfolio.information_ratio or 0)
    }


def _calculate_sector_allocation(holdings: List[PortfolioHolding]) -> Dict[str, float]:
    """Calculate sector allocation"""
    active_holdings = [h for h in holdings if h.is_active]
    if not active_holdings:
        return {}

    total_value = sum(float(h.market_value or 0) for h in active_holdings)
    if total_value == 0:
        return {}

    sector_allocation = {}
    for holding in active_holdings:
        sector = holding.bond.sector or 'Other'
        weight = float(holding.market_value or 0) / total_value * 100
        sector_allocation[sector] = sector_allocation.get(sector, 0) + weight

    return sector_allocation


def _calculate_rating_distribution(holdings: List[PortfolioHolding]) -> Dict[str, float]:
    """Calculate credit rating distribution"""
    active_holdings = [h for h in holdings if h.is_active]
    if not active_holdings:
        return {}

    total_value = sum(float(h.market_value or 0) for h in active_holdings)
    if total_value == 0:
        return {}

    rating_distribution = {}
    for holding in active_holdings:
        # Get latest rating
        rating = 'NR'  # Not Rated
        if holding.bond.ratings:
            latest_rating = max(holding.bond.ratings, key=lambda r: r.rating_date)
            rating = latest_rating.rating

        weight = float(holding.market_value or 0) / total_value * 100
        rating_distribution[rating] = rating_distribution.get(rating, 0) + weight

    return rating_distribution


def _calculate_maturity_distribution(holdings: List[PortfolioHolding]) -> Dict[str, float]:
    """Calculate maturity distribution"""
    active_holdings = [h for h in holdings if h.is_active]
    if not active_holdings:
        return {}

    total_value = sum(float(h.market_value or 0) for h in active_holdings)
    if total_value == 0:
        return {}

    maturity_distribution = {}
    for holding in active_holdings:
        # Categorize by time to maturity
        time_to_maturity = holding.bond.time_to_maturity or 0

        if time_to_maturity <= 365:
            bucket = "0-1 Year"
        elif time_to_maturity <= 365 * 3:
            bucket = "1-3 Years"
        elif time_to_maturity <= 365 * 5:
            bucket = "3-5 Years"
        elif time_to_maturity <= 365 * 10:
            bucket = "5-10 Years"
        else:
            bucket = "10+ Years"

        weight = float(holding.market_value or 0) / total_value * 100
        maturity_distribution[bucket] = maturity_distribution.get(bucket, 0) + weight

    return maturity_distribution


def _generate_portfolio_recommendations(
    portfolio: Portfolio,
    bond_analyses: List,
    liquidity_analysis: Dict,
    risk_metrics: Dict
) -> List[str]:
    """Generate portfolio recommendations"""
    recommendations = []

    # Liquidity recommendations
    liquidity_score = liquidity_analysis.get('liquidity_score', 50)
    if liquidity_score < 40:
        recommendations.append("Portfolio has low liquidity - consider adding more liquid bonds")

    # Risk recommendations
    if risk_metrics.get('average_credit_risk', 0) > 0.7:
        recommendations.append("High credit risk exposure - consider diversifying credit quality")

    if risk_metrics.get('average_duration_risk', 0) > 0.7:
        recommendations.append("High duration risk - consider shortening portfolio duration")

    # Concentration recommendations
    if risk_metrics.get('concentration_risk', 0) > 0.15:
        recommendations.append("High concentration risk - consider diversifying holdings")

    # Performance recommendations
    if len(bond_analyses) > 0:
        sell_recommendations = [a for a in bond_analyses if a.recommendation == 'SELL']
        if len(sell_recommendations) > 0:
            recommendations.append(f"{len(sell_recommendations)} bonds recommended for sale")

    return recommendations
