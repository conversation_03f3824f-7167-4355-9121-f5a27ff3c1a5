"""
Tests for service layer functionality
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, date, timedelta
from decimal import Decimal

from services.bond_analyzer import BondAnalyzer, BondAnalysisResult
from services.liquidity_scorer import LiquidityScorer, LiquidityMetrics
from services.similarity_engine import SimilarityEngine, SimilarityResult
from services.market_data import MarketDataService, MarketDataPoint
from app.models.bond import Bond, BondType, LiquidityTier


class TestBondAnalyzer:
    """Test BondAnalyzer service functionality."""
    
    @pytest.mark.asyncio
    async def test_initialize(self):
        """Test bond analyzer initialization."""
        analyzer = BondAnalyzer()
        
        with patch.object(analyzer.liquidity_scorer, 'initialize', new_callable=AsyncMock) as mock_liquidity_init, \
             patch.object(analyzer.market_data_service, 'initialize', new_callable=AsyncMock) as mock_market_init:
            
            await analyzer.initialize()
            
            assert analyzer.is_initialized is True
            mock_liquidity_init.assert_called_once()
            mock_market_init.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_analyze_bond(self, db_session, sample_bond_with_rating):
        """Test comprehensive bond analysis."""
        analyzer = BondAnalyzer()
        
        # Mock the dependencies
        with patch.object(analyzer.liquidity_scorer, 'calculate_liquidity_score', new_callable=AsyncMock) as mock_liquidity, \
             patch.object(analyzer.market_data_service, 'initialize', new_callable=AsyncMock):
            
            mock_liquidity.return_value = 75.0
            analyzer.is_initialized = True
            
            result = await analyzer.analyze_bond(str(sample_bond_with_rating.id), db_session)
            
            assert isinstance(result, BondAnalysisResult)
            assert result.bond_id == str(sample_bond_with_rating.id)
            assert result.cusip == sample_bond_with_rating.cusip
            assert result.liquidity_score == 75.0
            assert result.liquidity_tier == "tier_2"
            assert result.recommendation in ["BUY", "HOLD", "SELL", "AVOID"]
            assert 0 <= result.confidence_level <= 1
    
    @pytest.mark.asyncio
    async def test_analyze_liquidity(self, db_session, sample_bond):
        """Test liquidity analysis."""
        analyzer = BondAnalyzer()
        
        with patch.object(analyzer.liquidity_scorer, 'calculate_liquidity_score', new_callable=AsyncMock) as mock_liquidity:
            mock_liquidity.return_value = 85.0
            
            result = await analyzer._analyze_liquidity(sample_bond, db_session)
            
            assert result['score'] == 85.0
            assert result['tier'] == LiquidityTier.TIER_1.value
            assert result['risk_level'] == "LOW"
    
    def test_calculate_credit_risk(self):
        """Test credit risk calculation."""
        analyzer = BondAnalyzer()
        
        # Test investment grade
        ig_risk = analyzer._calculate_credit_risk("A", 100.0)
        assert 0 <= ig_risk <= 1
        assert ig_risk < 0.5  # Should be relatively low risk
        
        # Test high yield
        hy_risk = analyzer._calculate_credit_risk("BB", 500.0)
        assert 0 <= hy_risk <= 1
        assert hy_risk > 0.5  # Should be higher risk
    
    @pytest.mark.asyncio
    async def test_calculate_concentration_risk(self, db_session, sample_bond):
        """Test concentration risk calculation."""
        analyzer = BondAnalyzer()
        
        risk = await analyzer._calculate_concentration_risk(sample_bond, db_session)
        
        assert 0 <= risk <= 1
        assert isinstance(risk, float)


class TestLiquidityScorer:
    """Test LiquidityScorer service functionality."""
    
    @pytest.mark.asyncio
    async def test_initialize(self):
        """Test liquidity scorer initialization."""
        scorer = LiquidityScorer()
        
        await scorer.initialize()
        
        assert scorer.is_initialized is True
    
    @pytest.mark.asyncio
    async def test_calculate_liquidity_score(self, db_session, sample_bond):
        """Test liquidity score calculation."""
        scorer = LiquidityScorer()
        scorer.is_initialized = True
        
        with patch.object(scorer, '_calculate_liquidity_metrics', new_callable=AsyncMock) as mock_metrics, \
             patch.object(scorer, '_extract_liquidity_features') as mock_features, \
             patch.object(scorer, '_get_neural_network_score') as mock_nn, \
             patch.object(scorer, '_get_random_forest_score') as mock_rf, \
             patch.object(scorer, '_get_rule_based_score') as mock_rule:
            
            # Mock the metrics
            mock_metrics.return_value = LiquidityMetrics(
                bond_id=str(sample_bond.id),
                cusip=sample_bond.cusip,
                avg_daily_volume=5000000.0,
                volume_volatility=0.2,
                volume_trend=100000.0,
                bid_ask_spread=0.25,
                spread_volatility=0.1,
                market_depth_score=0.8,
                trading_frequency=0.7,
                days_since_last_trade=2,
                price_impact_score=0.9,
                liquidity_score=0.0,
                liquidity_tier="",
                confidence_level=0.8
            )
            
            mock_features.return_value = [0.5] * 20  # Mock feature array
            mock_nn.return_value = 75.0
            mock_rf.return_value = 70.0
            mock_rule.return_value = 80.0
            
            score = await scorer.calculate_liquidity_score(str(sample_bond.id), db_session)
            
            assert 0 <= score <= 100
            assert isinstance(score, float)
            
            # Verify bond was updated
            assert sample_bond.liquidity_score == score
            assert sample_bond.liquidity_tier is not None
    
    @pytest.mark.asyncio
    async def test_calculate_volume_metrics(self, db_session, sample_bond):
        """Test volume metrics calculation."""
        scorer = LiquidityScorer()
        
        # Add some price data
        from app.models.bond import BondPrice
        
        price1 = BondPrice(
            bond_id=sample_bond.id,
            price_date=date.today() - timedelta(days=5),
            price=Decimal("102.00"),
            volume=Decimal("1000000.00")
        )
        price2 = BondPrice(
            bond_id=sample_bond.id,
            price_date=date.today() - timedelta(days=3),
            price=Decimal("102.50"),
            volume=Decimal("1500000.00")
        )
        
        db_session.add_all([price1, price2])
        db_session.commit()
        
        metrics = await scorer._calculate_volume_metrics(sample_bond, db_session)
        
        assert 'avg_volume' in metrics
        assert 'volatility' in metrics
        assert 'trend' in metrics
        assert metrics['avg_volume'] > 0
    
    def test_determine_liquidity_tier(self):
        """Test liquidity tier determination."""
        scorer = LiquidityScorer()
        
        assert scorer._determine_liquidity_tier(85.0) == LiquidityTier.TIER_1.value
        assert scorer._determine_liquidity_tier(65.0) == LiquidityTier.TIER_2.value
        assert scorer._determine_liquidity_tier(45.0) == LiquidityTier.TIER_3.value
        assert scorer._determine_liquidity_tier(25.0) == LiquidityTier.TIER_4.value
    
    def test_get_rule_based_score(self):
        """Test rule-based scoring."""
        scorer = LiquidityScorer()
        
        # Create mock metrics
        metrics = LiquidityMetrics(
            bond_id="test",
            cusip="TEST123",
            avg_daily_volume=10000000.0,  # High volume
            volume_volatility=0.1,        # Low volatility
            volume_trend=0.0,
            bid_ask_spread=0.2,           # Low spread
            spread_volatility=0.05,
            market_depth_score=0.8,       # Good depth
            trading_frequency=0.9,        # High frequency
            days_since_last_trade=1,      # Recent trade
            price_impact_score=0.9,       # Low impact
            liquidity_score=0.0,
            liquidity_tier="",
            confidence_level=0.8
        )
        
        score = scorer._get_rule_based_score(metrics)
        
        assert 0 <= score <= 100
        assert score > 70  # Should be high given good metrics


class TestSimilarityEngine:
    """Test SimilarityEngine service functionality."""
    
    @pytest.mark.asyncio
    async def test_initialize(self):
        """Test similarity engine initialization."""
        engine = SimilarityEngine()
        
        with patch.object(engine.quantum_engine, 'initialize', new_callable=AsyncMock) as mock_quantum_init:
            await engine.initialize()
            
            assert engine.is_initialized is True
            mock_quantum_init.assert_called_once()
    
    def test_extract_similarity_features(self):
        """Test feature extraction for similarity calculation."""
        engine = SimilarityEngine()
        
        bond_data = {
            'id': 'test-id',
            'cusip': 'TEST123',
            'yield_to_maturity': 4.5,
            'duration': 5.2,
            'current_price': 102.5,
            'liquidity_score': 75.0,
            'credit_rating': 'A',
            'bond_type': 'corporate',
            'sector': 'technology',
            'country': 'US',
            'is_callable': False,
            'is_puttable': False,
            'time_to_maturity': 1825  # ~5 years
        }
        
        features = engine.extract_similarity_features(bond_data)
        
        assert len(features) == engine.feature_dim
        assert all(isinstance(f, float) for f in features)
        assert all(0 <= f <= 1 for f in features[:15])  # Most features should be normalized
    
    def test_rating_to_numeric(self):
        """Test credit rating to numeric conversion."""
        engine = SimilarityEngine()
        
        assert engine._rating_to_numeric('AAA') == 1.0
        assert engine._rating_to_numeric('BBB') == 0.6
        assert engine._rating_to_numeric('D') == 0.05
        assert engine._rating_to_numeric('INVALID') == 0.6  # Default
    
    def test_encode_categorical(self):
        """Test categorical encoding."""
        engine = SimilarityEngine()
        
        categories = ['cat1', 'cat2', 'cat3']
        
        # Test valid category
        encoding = engine._encode_categorical('cat2', categories)
        assert encoding == [0.0, 1.0, 0.0]
        
        # Test invalid category (should default to last)
        encoding = engine._encode_categorical('invalid', categories)
        assert encoding == [0.0, 0.0, 1.0]
    
    @pytest.mark.asyncio
    async def test_calculate_similarity(self):
        """Test similarity calculation between two bonds."""
        engine = SimilarityEngine()
        engine.is_initialized = True
        
        bond1_data = {
            'id': 'bond1',
            'cusip': 'TEST001',
            'yield_to_maturity': 4.5,
            'duration': 5.0,
            'liquidity_score': 70.0,
            'credit_rating': 'A',
            'bond_type': 'corporate',
            'sector': 'technology'
        }
        
        bond2_data = {
            'id': 'bond2',
            'cusip': 'TEST002',
            'yield_to_maturity': 4.7,
            'duration': 5.2,
            'liquidity_score': 75.0,
            'credit_rating': 'A',
            'bond_type': 'corporate',
            'sector': 'technology'
        }
        
        with patch.object(engine.quantum_engine, 'create_quantum_state', new_callable=AsyncMock), \
             patch.object(engine.quantum_engine, 'calculate_quantum_similarity', new_callable=AsyncMock) as mock_quantum:
            
            mock_quantum.return_value = 0.8
            
            result = await engine.calculate_similarity(bond1_data, bond2_data)
            
            assert isinstance(result, SimilarityResult)
            assert result.target_bond_id == 'bond1'
            assert result.similar_bond_id == 'bond2'
            assert 0 <= result.similarity_score <= 1
            assert 0 <= result.confidence_score <= 1
            assert isinstance(result.feature_similarities, dict)


class TestMarketDataService:
    """Test MarketDataService functionality."""
    
    @pytest.mark.asyncio
    async def test_initialize(self):
        """Test market data service initialization."""
        service = MarketDataService()
        
        with patch.object(service, '_test_api_connections', new_callable=AsyncMock):
            await service.initialize()
            
            assert service.is_initialized is True
            assert service.session is not None
    
    @pytest.mark.asyncio
    async def test_get_bond_price_cached(self):
        """Test bond price retrieval with caching."""
        service = MarketDataService()
        service.is_initialized = True
        
        # Mock cached data
        cached_data = MarketDataPoint(
            symbol="TEST",
            cusip="TEST123",
            price=102.5,
            yield_value=4.5,
            bid_price=102.25,
            ask_price=102.75,
            volume=1000000,
            timestamp=datetime.utcnow(),
            source="cache"
        )
        
        service.price_cache["TEST123"] = (cached_data, datetime.utcnow())
        
        result = await service.get_bond_price("TEST123", "TEST")
        
        assert result == cached_data
        assert result.source == "cache"
    
    @pytest.mark.asyncio
    async def test_check_rate_limit(self):
        """Test API rate limiting."""
        service = MarketDataService()
        
        # Test initial request (should pass)
        assert await service._check_rate_limit('yahoo') is True
        
        # Fill up the rate limit
        for _ in range(service.apis['yahoo']['rate_limit'] - 1):
            await service._check_rate_limit('yahoo')
        
        # Next request should fail
        assert await service._check_rate_limit('yahoo') is False
    
    def test_get_client_ip(self):
        """Test client IP extraction."""
        service = MarketDataService()
        
        # Mock request object
        class MockRequest:
            def __init__(self, headers, client_host=None):
                self.headers = headers
                self.client = Mock()
                self.client.host = client_host
        
        # Test X-Forwarded-For header
        request = MockRequest({"X-Forwarded-For": "***********, ********"})
        assert service._get_client_ip(request) == "***********"
        
        # Test X-Real-IP header
        request = MockRequest({"X-Real-IP": "***********"})
        assert service._get_client_ip(request) == "***********"
        
        # Test client.host fallback
        request = MockRequest({}, "***********")
        assert service._get_client_ip(request) == "***********"
    
    @pytest.mark.asyncio
    async def test_get_treasury_rates(self):
        """Test treasury rates retrieval."""
        service = MarketDataService()
        service.is_initialized = True
        service.apis['fred']['api_key'] = 'test_key'
        
        with patch.object(service, '_fetch_fred_data', new_callable=AsyncMock) as mock_fred:
            mock_fred.return_value = [{'value': '4.25', 'date': '2024-01-01'}]
            
            rates = await service.get_treasury_rates()
            
            assert isinstance(rates, dict)
            # Should have called FRED for each treasury maturity
            assert mock_fred.call_count >= 1
