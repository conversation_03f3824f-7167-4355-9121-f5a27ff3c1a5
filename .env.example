# Environment Configuration for LiquidBond

# Application Environment
NODE_ENV=development
DEBUG=true

# Server Configuration
HOST=0.0.0.0
PORT=8000

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/liquidbond
REDIS_URL=redis://localhost:6379/0
MONGODB_URL=mongodb://localhost:27017/liquidbond

# InfluxDB Configuration (Time Series Data)
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-influxdb-token
INFLUXDB_ORG=liquidbond
INFLUXDB_BUCKET=market_data

# Security Configuration
SECRET_KEY=your-super-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# External API Keys
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-api-key
FRED_API_KEY=your-fred-api-key
QUANDL_API_KEY=your-quandl-api-key

# AI/ML Configuration
MODEL_PATH=./ml_models
BATCH_SIZE=32
MAX_WORKERS=4

# Trading Configuration
ENABLE_PAPER_TRADING=true
MAX_POSITION_SIZE=1000000.0

# Monitoring Configuration
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=INFO

# Frontend Configuration
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/ws
VITE_APP_NAME=LiquidBond
VITE_APP_VERSION=1.0.0

# Feature Flags
VITE_ENABLE_QUANTUM_ANALYSIS=true
VITE_ENABLE_REAL_TIME_DATA=true
VITE_ENABLE_PAPER_TRADING=true
VITE_ENABLE_ANALYTICS=true

# Third-party Services
VITE_GOOGLE_ANALYTICS_ID=your-ga-id
VITE_SENTRY_DSN=your-frontend-sentry-dsn

# Development Tools
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_MOCK_DATA=false
