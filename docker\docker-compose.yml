version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: liquidbond-postgres
    environment:
      POSTGRES_DB: liquidbond
      POSTGRES_USER: liquidbond_user
      POSTGRES_PASSWORD: liquidbond_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - liquidbond-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U liquidbond_user -d liquidbond"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: liquidbond-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - liquidbond-network
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass liquidbond_redis_password
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB for User Preferences
  mongodb:
    image: mongo:7
    container_name: liquidbond-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: liquidbond_mongo
      MONGO_INITDB_ROOT_PASSWORD: liquidbond_mongo_password
      MONGO_INITDB_DATABASE: liquidbond
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - liquidbond-network
    restart: unless-stopped
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/liquidbond --quiet
      interval: 30s
      timeout: 10s
      retries: 3

  # InfluxDB for Time Series Data
  influxdb:
    image: influxdb:2.7-alpine
    container_name: liquidbond-influxdb
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: liquidbond_influx
      DOCKER_INFLUXDB_INIT_PASSWORD: liquidbond_influx_password
      DOCKER_INFLUXDB_INIT_ORG: liquidbond
      DOCKER_INFLUXDB_INIT_BUCKET: market_data
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: liquidbond-super-secret-admin-token
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
    networks:
      - liquidbond-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
    container_name: liquidbond-backend
    environment:
      DATABASE_URL: **************************************************************/liquidbond
      REDIS_URL: redis://:liquidbond_redis_password@redis:6379/0
      MONGODB_URL: *****************************************************************************
      INFLUXDB_URL: http://influxdb:8086
      INFLUXDB_TOKEN: liquidbond-super-secret-admin-token
      INFLUXDB_ORG: liquidbond
      INFLUXDB_BUCKET: market_data
      SECRET_KEY: liquidbond-super-secret-key-change-in-production
      DEBUG: "false"
    ports:
      - "8000:8000"
    volumes:
      - ../backend:/app
      - ml_models:/app/ml_models
    networks:
      - liquidbond-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      mongodb:
        condition: service_healthy
      influxdb:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend
  frontend:
    build:
      context: ../frontend
      dockerfile: ../docker/Dockerfile.frontend
    container_name: liquidbond-frontend
    environment:
      VITE_API_BASE_URL: http://localhost:8000/api/v1
      VITE_WS_URL: ws://localhost:8000/ws
    ports:
      - "3000:3000"
    volumes:
      - ../frontend:/app
      - /app/node_modules
    networks:
      - liquidbond-network
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: liquidbond-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    networks:
      - liquidbond-network
    depends_on:
      - backend
      - frontend
    restart: unless-stopped

  # Celery Worker for Background Tasks
  celery-worker:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
    container_name: liquidbond-celery-worker
    command: celery -A app.celery worker --loglevel=info
    environment:
      DATABASE_URL: **************************************************************/liquidbond
      REDIS_URL: redis://:liquidbond_redis_password@redis:6379/0
      MONGODB_URL: *****************************************************************************
    volumes:
      - ../backend:/app
      - ml_models:/app/ml_models
    networks:
      - liquidbond-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Celery Beat for Scheduled Tasks
  celery-beat:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
    container_name: liquidbond-celery-beat
    command: celery -A app.celery beat --loglevel=info
    environment:
      DATABASE_URL: **************************************************************/liquidbond
      REDIS_URL: redis://:liquidbond_redis_password@redis:6379/0
    volumes:
      - ../backend:/app
      - celery_beat_data:/app/celerybeat-schedule
    networks:
      - liquidbond-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Prometheus for Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: liquidbond-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - liquidbond-network
    restart: unless-stopped

  # Grafana for Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: liquidbond-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: liquidbond_grafana_password
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - liquidbond-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  mongodb_data:
  influxdb_data:
  influxdb_config:
  ml_models:
  celery_beat_data:
  prometheus_data:
  grafana_data:

networks:
  liquidbond-network:
    driver: bridge
