import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'
import api from './api'

// Auth context
const AuthContext = createContext()

// Auth action types
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  UPDATE_USER: 'UPDATE_USER',
  REFRESH_TOKEN: 'REFRESH_TOKEN',
}

// Initial auth state
const initialState = {
  user: null,
  token: localStorage.getItem('token'),
  isAuthenticated: false,
  isLoading: true,
  error: null,
}

// Auth reducer
function authReducer(state, action) {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
      return {
        ...state,
        isLoading: true,
        error: null,
      }
    
    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      }
    
    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      }
    
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      }
    
    case AUTH_ACTIONS.UPDATE_USER:
      return {
        ...state,
        user: { ...state.user, ...action.payload },
      }
    
    case AUTH_ACTIONS.REFRESH_TOKEN:
      return {
        ...state,
        token: action.payload,
      }
    
    default:
      return state
  }
}

// Auth provider component
export function AuthProvider({ children }) {
  const [state, dispatch] = useReducer(authReducer, initialState)
  const queryClient = useQueryClient()

  // Get current user query
  const { data: userData, isLoading: userLoading } = useQuery(
    'currentUser',
    () => api.get('/auth/me'),
    {
      enabled: !!state.token,
      retry: false,
      onSuccess: (data) => {
        dispatch({
          type: AUTH_ACTIONS.LOGIN_SUCCESS,
          payload: {
            user: data.user,
            token: state.token,
          },
        })
      },
      onError: () => {
        // Token is invalid, logout
        logout()
      },
    }
  )

  // Login mutation
  const loginMutation = useMutation(
    (credentials) => api.post('/auth/login', credentials),
    {
      onMutate: () => {
        dispatch({ type: AUTH_ACTIONS.LOGIN_START })
      },
      onSuccess: (data) => {
        const { user, access_token } = data
        
        // Store token
        localStorage.setItem('token', access_token)
        
        // Update state
        dispatch({
          type: AUTH_ACTIONS.LOGIN_SUCCESS,
          payload: {
            user,
            token: access_token,
          },
        })
        
        // Set default auth header
        api.defaults.headers.common['Authorization'] = `Bearer ${access_token}`
        
        toast.success(`Welcome back, ${user.first_name}!`)
      },
      onError: (error) => {
        const message = error.response?.data?.detail || 'Login failed'
        
        dispatch({
          type: AUTH_ACTIONS.LOGIN_FAILURE,
          payload: message,
        })
        
        toast.error(message)
      },
    }
  )

  // Logout function
  const logout = async () => {
    try {
      // Call logout endpoint
      await api.post('/auth/logout')
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Clear local storage
      localStorage.removeItem('token')
      
      // Clear auth header
      delete api.defaults.headers.common['Authorization']
      
      // Clear React Query cache
      queryClient.clear()
      
      // Update state
      dispatch({ type: AUTH_ACTIONS.LOGOUT })
      
      toast.success('Logged out successfully')
    }
  }

  // Update user profile
  const updateUser = useMutation(
    (userData) => api.put('/auth/profile', userData),
    {
      onSuccess: (data) => {
        dispatch({
          type: AUTH_ACTIONS.UPDATE_USER,
          payload: data.user,
        })
        
        toast.success('Profile updated successfully')
      },
      onError: (error) => {
        const message = error.response?.data?.detail || 'Update failed'
        toast.error(message)
      },
    }
  )

  // Change password
  const changePassword = useMutation(
    (passwordData) => api.put('/auth/change-password', passwordData),
    {
      onSuccess: () => {
        toast.success('Password changed successfully')
      },
      onError: (error) => {
        const message = error.response?.data?.detail || 'Password change failed'
        toast.error(message)
      },
    }
  )

  // Setup token interceptor
  useEffect(() => {
    if (state.token) {
      api.defaults.headers.common['Authorization'] = `Bearer ${state.token}`
    }
  }, [state.token])

  // Token refresh interceptor
  useEffect(() => {
    const interceptor = api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config
        
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true
          
          try {
            const response = await api.post('/auth/refresh')
            const { access_token } = response.data
            
            localStorage.setItem('token', access_token)
            api.defaults.headers.common['Authorization'] = `Bearer ${access_token}`
            
            dispatch({
              type: AUTH_ACTIONS.REFRESH_TOKEN,
              payload: access_token,
            })
            
            return api(originalRequest)
          } catch (refreshError) {
            logout()
            return Promise.reject(refreshError)
          }
        }
        
        return Promise.reject(error)
      }
    )

    return () => {
      api.interceptors.response.eject(interceptor)
    }
  }, [])

  // Check if user has required role
  const hasRole = (requiredRole) => {
    if (!state.user) return false
    
    const userRole = state.user.role
    
    // Admin has access to everything
    if (userRole === 'admin') return true
    
    // Role hierarchy
    const roleHierarchy = {
      admin: 5,
      portfolio_manager: 4,
      trader: 3,
      risk_manager: 3,
      analyst: 2,
      viewer: 1,
    }
    
    const userLevel = roleHierarchy[userRole] || 0
    const requiredLevel = roleHierarchy[requiredRole] || 0
    
    return userLevel >= requiredLevel
  }

  // Check if user can perform action
  const canPerform = (action) => {
    if (!state.user) return false
    
    const permissions = {
      view_portfolio: ['admin', 'portfolio_manager', 'trader', 'risk_manager', 'analyst', 'viewer'],
      manage_portfolio: ['admin', 'portfolio_manager'],
      execute_trades: ['admin', 'portfolio_manager', 'trader'],
      view_analytics: ['admin', 'portfolio_manager', 'trader', 'risk_manager', 'analyst'],
      manage_users: ['admin'],
      view_bonds: ['admin', 'portfolio_manager', 'trader', 'risk_manager', 'analyst', 'viewer'],
    }
    
    const allowedRoles = permissions[action] || []
    return allowedRoles.includes(state.user.role)
  }

  const value = {
    // State
    user: state.user,
    token: state.token,
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading || userLoading,
    error: state.error,
    
    // Actions
    login: loginMutation.mutate,
    logout,
    updateUser: updateUser.mutate,
    changePassword: changePassword.mutate,
    
    // Permissions
    hasRole,
    canPerform,
    
    // Loading states
    isLoggingIn: loginMutation.isLoading,
    isUpdatingUser: updateUser.isLoading,
    isChangingPassword: changePassword.isLoading,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  
  return context
}
