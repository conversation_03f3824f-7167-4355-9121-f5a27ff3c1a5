"""
Tests for API endpoints
"""

import pytest
from unittest.mock import patch, AsyncMock
from datetime import datetime, date, timedelta
from decimal import Decimal

from fastapi import status
from app.models.user import UserRole
from app.models.bond import BondType
from app.models.portfolio import PortfolioType
from app.models.trade import OrderSide, OrderType


class TestAuthAPI:
    """Test authentication API endpoints."""
    
    def test_register_user(self, client, db_session):
        """Test user registration."""
        user_data = {
            "email": "<EMAIL>",
            "username": "newuser",
            "password": "NewPassword123!",
            "first_name": "New",
            "last_name": "User",
            "company": "Test Company",
            "job_title": "Analyst"
        }
        
        response = client.post("/api/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["email"] == user_data["email"]
        assert data["username"] == user_data["username"]
        assert data["role"] == UserRole.VIEWER.value  # Default role
        assert "id" in data
    
    def test_register_duplicate_user(self, client, sample_user):
        """Test registration with duplicate email/username."""
        user_data = {
            "email": sample_user.email,
            "username": "different_username",
            "password": "Password123!",
            "first_name": "Test",
            "last_name": "User"
        }
        
        response = client.post("/api/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "already exists" in response.json()["detail"]
    
    def test_login_success(self, client, sample_user):
        """Test successful login."""
        login_data = {
            "username": sample_user.username,
            "password": "TestPassword123!"
        }
        
        response = client.post("/api/auth/login", json=login_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
        assert data["user_id"] == str(sample_user.id)
        assert data["role"] == sample_user.role
    
    def test_login_invalid_credentials(self, client, sample_user):
        """Test login with invalid credentials."""
        login_data = {
            "username": sample_user.username,
            "password": "WrongPassword"
        }
        
        response = client.post("/api/auth/login", json=login_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Incorrect username or password" in response.json()["detail"]
    
    def test_get_current_user(self, client, auth_headers):
        """Test getting current user information."""
        response = client.get("/api/auth/me", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "id" in data
        assert "email" in data
        assert "username" in data
        assert "role" in data
    
    def test_change_password(self, client, auth_headers):
        """Test password change."""
        password_data = {
            "current_password": "TestPassword123!",
            "new_password": "NewPassword123!"
        }
        
        response = client.post("/api/auth/change-password", json=password_data, headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        assert "Password changed successfully" in response.json()["message"]
    
    def test_unauthorized_access(self, client):
        """Test accessing protected endpoint without authentication."""
        response = client.get("/api/auth/me")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestBondsAPI:
    """Test bonds API endpoints."""
    
    def test_get_bonds(self, client, auth_headers, sample_bond):
        """Test getting bonds list."""
        response = client.get("/api/bonds/", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        
        bond_data = data[0]
        assert bond_data["cusip"] == sample_bond.cusip
        assert bond_data["issuer_name"] == sample_bond.issuer_name
    
    def test_get_bond_by_id(self, client, auth_headers, sample_bond):
        """Test getting specific bond by ID."""
        response = client.get(f"/api/bonds/{sample_bond.id}", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == str(sample_bond.id)
        assert data["cusip"] == sample_bond.cusip
        assert data["issuer_name"] == sample_bond.issuer_name
    
    def test_get_nonexistent_bond(self, client, auth_headers):
        """Test getting non-existent bond."""
        fake_id = "00000000-0000-0000-0000-000000000000"
        response = client.get(f"/api/bonds/{fake_id}", headers=auth_headers)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_search_bonds(self, client, auth_headers, sample_bond):
        """Test bond search functionality."""
        search_params = {
            "query": sample_bond.issuer_name,
            "limit": 10
        }
        
        response = client.get("/api/bonds/search", params=search_params, headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        assert any(bond["cusip"] == sample_bond.cusip for bond in data)
    
    def test_filter_bonds(self, client, auth_headers, sample_bond):
        """Test bond filtering."""
        filter_params = {
            "bond_type": sample_bond.bond_type,
            "min_yield": 0,
            "max_yield": 10
        }
        
        response = client.get("/api/bonds/", params=filter_params, headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        # All returned bonds should match the filter
        for bond in data:
            assert bond["bond_type"] == sample_bond.bond_type


class TestPortfolioAPI:
    """Test portfolio API endpoints."""
    
    def test_get_user_portfolios(self, client, auth_headers, sample_portfolio):
        """Test getting user's portfolios."""
        response = client.get("/api/portfolio/", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        
        portfolio_data = data[0]
        assert portfolio_data["id"] == str(sample_portfolio.id)
        assert portfolio_data["name"] == sample_portfolio.name
    
    def test_create_portfolio(self, client, auth_headers):
        """Test creating a new portfolio."""
        portfolio_data = {
            "name": "New Test Portfolio",
            "description": "A new portfolio for testing",
            "portfolio_type": PortfolioType.INSTITUTIONAL.value,
            "base_currency": "USD",
            "benchmark_name": "Bloomberg Aggregate",
            "max_position_size": 15.0
        }
        
        response = client.post("/api/portfolio/", json=portfolio_data, headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["name"] == portfolio_data["name"]
        assert data["portfolio_type"] == portfolio_data["portfolio_type"]
        assert "id" in data
    
    def test_get_portfolio_by_id(self, client, auth_headers, sample_portfolio):
        """Test getting specific portfolio."""
        response = client.get(f"/api/portfolio/{sample_portfolio.id}", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == str(sample_portfolio.id)
        assert data["name"] == sample_portfolio.name
    
    def test_update_portfolio(self, client, auth_headers, sample_portfolio):
        """Test updating portfolio."""
        update_data = {
            "name": "Updated Portfolio Name",
            "description": "Updated description"
        }
        
        response = client.put(f"/api/portfolio/{sample_portfolio.id}", json=update_data, headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]
    
    def test_get_portfolio_holdings(self, client, auth_headers, sample_holding):
        """Test getting portfolio holdings."""
        portfolio_id = sample_holding.portfolio_id
        
        response = client.get(f"/api/portfolio/{portfolio_id}/holdings", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        
        holding_data = data[0]
        assert holding_data["id"] == str(sample_holding.id)
        assert holding_data["bond_cusip"] == sample_holding.bond.cusip
    
    def test_add_portfolio_holding(self, client, auth_headers, sample_portfolio, sample_bond):
        """Test adding a holding to portfolio."""
        holding_data = {
            "bond_cusip": sample_bond.cusip,
            "quantity": 1000000.0,
            "average_cost": 101.50
        }
        
        response = client.post(
            f"/api/portfolio/{sample_portfolio.id}/holdings",
            json=holding_data,
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["bond_cusip"] == holding_data["bond_cusip"]
        assert float(data["quantity"]) == holding_data["quantity"]
    
    @pytest.mark.asyncio
    async def test_analyze_portfolio(self, client, auth_headers, sample_portfolio):
        """Test portfolio analysis."""
        with patch('services.bond_analyzer.BondAnalyzer.initialize', new_callable=AsyncMock), \
             patch('services.liquidity_scorer.LiquidityScorer.initialize', new_callable=AsyncMock), \
             patch('services.bond_analyzer.BondAnalyzer.analyze_portfolio_bonds', new_callable=AsyncMock) as mock_analyze, \
             patch('services.liquidity_scorer.LiquidityScorer.calculate_portfolio_liquidity', new_callable=AsyncMock) as mock_liquidity:
            
            mock_analyze.return_value = []
            mock_liquidity.return_value = {
                'liquidity_score': 75.0,
                'tier_distribution': {'tier_1': 50.0, 'tier_2': 50.0}
            }
            
            response = client.get(f"/api/portfolio/{sample_portfolio.id}/analysis", headers=auth_headers)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "portfolio_id" in data
            assert "liquidity_analysis" in data
            assert "risk_metrics" in data


class TestTradingAPI:
    """Test trading API endpoints."""
    
    def test_get_user_trades(self, client, auth_headers, sample_trade):
        """Test getting user's trades."""
        response = client.get("/api/trading/", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        
        trade_data = data[0]
        assert trade_data["id"] == str(sample_trade.id)
        assert trade_data["trade_id"] == sample_trade.trade_id
    
    def test_create_trade(self, client, auth_headers, sample_portfolio, sample_bond):
        """Test creating a new trade."""
        trade_data = {
            "portfolio_id": str(sample_portfolio.id),
            "bond_cusip": sample_bond.cusip,
            "side": OrderSide.BUY.value,
            "quantity": 500000.0,
            "order_type": OrderType.MARKET.value,
            "notes": "Test trade"
        }
        
        response = client.post("/api/trading/", json=trade_data, headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["bond_cusip"] == trade_data["bond_cusip"]
        assert data["side"] == trade_data["side"]
        assert float(data["quantity"]) == trade_data["quantity"]
        assert "trade_id" in data
    
    def test_get_trade_by_id(self, client, auth_headers, sample_trade):
        """Test getting specific trade."""
        response = client.get(f"/api/trading/{sample_trade.id}", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == str(sample_trade.id)
        assert data["trade_id"] == sample_trade.trade_id
    
    def test_cancel_trade(self, client, auth_headers, db_session, sample_user, sample_portfolio, sample_bond):
        """Test cancelling a pending trade."""
        from app.models.trade import Trade, TradeStatus
        
        # Create a pending trade
        pending_trade = Trade(
            user_id=sample_user.id,
            portfolio_id=sample_portfolio.id,
            bond_id=sample_bond.id,
            trade_id="TRD-PENDING-001",
            side=OrderSide.BUY.value,
            quantity=Decimal("500000.00"),
            price=Decimal("102.00"),
            status=TradeStatus.PENDING.value
        )
        
        db_session.add(pending_trade)
        db_session.commit()
        
        response = client.post(f"/api/trading/cancel/{pending_trade.id}", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        assert "cancelled successfully" in response.json()["message"]
    
    @pytest.mark.asyncio
    async def test_get_alternative_trades(self, client, auth_headers, sample_portfolio, sample_bond):
        """Test getting alternative trade recommendations."""
        with patch('services.similarity_engine.SimilarityEngine.initialize', new_callable=AsyncMock), \
             patch('services.similarity_engine.SimilarityEngine.find_similar_bonds', new_callable=AsyncMock) as mock_similar:
            
            mock_similar.return_value = []
            
            request_data = {
                "original_bond_cusip": sample_bond.cusip,
                "portfolio_id": str(sample_portfolio.id),
                "side": OrderSide.BUY.value,
                "quantity": 1000000.0,
                "max_alternatives": 5
            }
            
            response = client.post("/api/trading/alternatives", json=request_data, headers=auth_headers)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "original_bond" in data
            assert "alternatives" in data
            assert data["original_bond"]["cusip"] == sample_bond.cusip


class TestAPIPermissions:
    """Test API permission and authorization."""
    
    def test_viewer_cannot_trade(self, client, db_session):
        """Test that viewer role cannot execute trades."""
        from tests.conftest import UserFactory
        
        viewer = UserFactory.create_user(db_session, role=UserRole.VIEWER.value)
        
        from api.auth import create_access_token
        token_data = {"sub": str(viewer.id), "username": viewer.username, "role": viewer.role}
        access_token = create_access_token(token_data)
        headers = {"Authorization": f"Bearer {access_token}"}
        
        trade_data = {
            "portfolio_id": "fake-id",
            "bond_cusip": "FAKE123",
            "side": "buy",
            "quantity": 100000.0
        }
        
        response = client.post("/api/trading/", json=trade_data, headers=headers)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
    
    def test_cross_user_portfolio_access(self, client, db_session, sample_user):
        """Test that users cannot access other users' portfolios."""
        from tests.conftest import UserFactory
        
        other_user = UserFactory.create_user(db_session, email="<EMAIL>", username="otheruser")
        
        from api.auth import create_access_token
        token_data = {"sub": str(other_user.id), "username": other_user.username, "role": other_user.role}
        access_token = create_access_token(token_data)
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Try to access sample_user's portfolio
        from app.models.portfolio import Portfolio
        portfolio = db_session.query(Portfolio).filter(Portfolio.user_id == sample_user.id).first()
        
        if portfolio:
            response = client.get(f"/api/portfolio/{portfolio.id}", headers=headers)
            assert response.status_code == status.HTTP_404_NOT_FOUND
