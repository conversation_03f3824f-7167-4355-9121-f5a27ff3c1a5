"""
Database configuration and connection management for LiquidBond
"""

import asyncio
from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import redis.asyncio as redis
from motor.motor_asyncio import AsyncIOMotorClient
from influxdb_client.client.influxdb_client_async import InfluxDBClientAsync

from app.config import settings

# SQLAlchemy setup
DATABASE_URL = settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")

engine = create_async_engine(
    DATABASE_URL,
    echo=settings.DEBUG,
    pool_size=20,
    max_overflow=0,
    pool_pre_ping=True,
    pool_recycle=300,
)

async_session_maker = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
)

Base = declarative_base()
metadata = MetaData()


# Database session dependency
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Get database session"""
    async with async_session_maker() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


# Redis connection
class RedisManager:
    """Redis connection manager"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
    
    async def connect(self):
        """Connect to Redis"""
        self.redis_client = redis.from_url(
            settings.REDIS_URL,
            encoding="utf-8",
            decode_responses=True,
            max_connections=20,
        )
        
    async def disconnect(self):
        """Disconnect from Redis"""
        if self.redis_client:
            await self.redis_client.close()
    
    async def get_client(self) -> redis.Redis:
        """Get Redis client"""
        if not self.redis_client:
            await self.connect()
        return self.redis_client


redis_manager = RedisManager()


# MongoDB connection
class MongoManager:
    """MongoDB connection manager"""
    
    def __init__(self):
        self.client: Optional[AsyncIOMotorClient] = None
        self.database = None
    
    async def connect(self):
        """Connect to MongoDB"""
        self.client = AsyncIOMotorClient(settings.MONGODB_URL)
        self.database = self.client.liquidbond
        
    async def disconnect(self):
        """Disconnect from MongoDB"""
        if self.client:
            self.client.close()
    
    async def get_database(self):
        """Get MongoDB database"""
        if not self.client:
            await self.connect()
        return self.database


mongo_manager = MongoManager()


# InfluxDB connection
class InfluxManager:
    """InfluxDB connection manager for time series data"""
    
    def __init__(self):
        self.client: Optional[InfluxDBClientAsync] = None
    
    async def connect(self):
        """Connect to InfluxDB"""
        self.client = InfluxDBClientAsync(
            url=settings.INFLUXDB_URL,
            token=settings.INFLUXDB_TOKEN,
            org=settings.INFLUXDB_ORG,
        )
        
    async def disconnect(self):
        """Disconnect from InfluxDB"""
        if self.client:
            await self.client.close()
    
    async def get_client(self) -> InfluxDBClientAsync:
        """Get InfluxDB client"""
        if not self.client:
            await self.connect()
        return self.client


influx_manager = InfluxManager()


# Database initialization
async def init_database():
    """Initialize all database connections"""
    try:
        # Test PostgreSQL connection
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        # Initialize Redis
        await redis_manager.connect()
        
        # Initialize MongoDB
        await mongo_manager.connect()
        
        # Initialize InfluxDB
        await influx_manager.connect()
        
        print("✅ All database connections initialized successfully")
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        raise


async def close_database():
    """Close all database connections"""
    try:
        await engine.dispose()
        await redis_manager.disconnect()
        await mongo_manager.disconnect()
        await influx_manager.disconnect()
        
        print("✅ All database connections closed successfully")
        
    except Exception as e:
        print(f"❌ Database cleanup failed: {e}")


# Context manager for database lifecycle
@asynccontextmanager
async def database_lifespan():
    """Database lifecycle context manager"""
    await init_database()
    try:
        yield
    finally:
        await close_database()


# Health check functions
async def check_database_health() -> dict:
    """Check health of all database connections"""
    health_status = {
        "postgresql": False,
        "redis": False,
        "mongodb": False,
        "influxdb": False,
    }
    
    # Check PostgreSQL
    try:
        async with async_session_maker() as session:
            await session.execute("SELECT 1")
        health_status["postgresql"] = True
    except Exception:
        pass
    
    # Check Redis
    try:
        redis_client = await redis_manager.get_client()
        await redis_client.ping()
        health_status["redis"] = True
    except Exception:
        pass
    
    # Check MongoDB
    try:
        db = await mongo_manager.get_database()
        await db.command("ping")
        health_status["mongodb"] = True
    except Exception:
        pass
    
    # Check InfluxDB
    try:
        client = await influx_manager.get_client()
        await client.ping()
        health_status["influxdb"] = True
    except Exception:
        pass
    
    return health_status
