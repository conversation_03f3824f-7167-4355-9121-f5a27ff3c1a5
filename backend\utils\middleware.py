"""
Middleware utilities for LiquidBond application
"""

import time
import logging
from typing import Dict, Optional, Callable, Any
from datetime import datetime, timedelta
from collections import defaultdict, deque
import asyncio
import json

from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

logger = logging.getLogger(__name__)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware with sliding window algorithm"""
    
    def __init__(
        self,
        app: ASGIApp,
        calls: int = 100,
        period: int = 60,
        per_ip: bool = True,
        per_user: bool = False,
        exclude_paths: Optional[list] = None
    ):
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.per_ip = per_ip
        self.per_user = per_user
        self.exclude_paths = exclude_paths or []
        
        # Storage for rate limiting (in production, use Redis)
        self.ip_requests: Dict[str, deque] = defaultdict(deque)
        self.user_requests: Dict[str, deque] = defaultdict(deque)
        
        # Cleanup task
        self._cleanup_task = None
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip rate limiting for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        # Start cleanup task if not running
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_old_requests())
        
        current_time = time.time()
        client_ip = self._get_client_ip(request)
        user_id = self._get_user_id(request)
        
        # Check IP-based rate limit
        if self.per_ip and client_ip:
            if not self._check_rate_limit(self.ip_requests[client_ip], current_time):
                return self._rate_limit_response(request)
        
        # Check user-based rate limit
        if self.per_user and user_id:
            if not self._check_rate_limit(self.user_requests[user_id], current_time):
                return self._rate_limit_response(request)
        
        # Record the request
        if self.per_ip and client_ip:
            self.ip_requests[client_ip].append(current_time)
        if self.per_user and user_id:
            self.user_requests[user_id].append(current_time)
        
        response = await call_next(request)
        
        # Add rate limit headers
        if self.per_ip and client_ip:
            remaining = max(0, self.calls - len(self.ip_requests[client_ip]))
            response.headers["X-RateLimit-Limit"] = str(self.calls)
            response.headers["X-RateLimit-Remaining"] = str(remaining)
            response.headers["X-RateLimit-Reset"] = str(int(current_time + self.period))
        
        return response
    
    def _check_rate_limit(self, request_times: deque, current_time: float) -> bool:
        """Check if request is within rate limit"""
        # Remove old requests outside the time window
        cutoff_time = current_time - self.period
        while request_times and request_times[0] < cutoff_time:
            request_times.popleft()
        
        # Check if under limit
        return len(request_times) < self.calls
    
    def _get_client_ip(self, request: Request) -> Optional[str]:
        """Get client IP address"""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else None
    
    def _get_user_id(self, request: Request) -> Optional[str]:
        """Extract user ID from request (if authenticated)"""
        # This would extract user ID from JWT token or session
        # Simplified implementation
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            try:
                import jwt
                from api.auth import SECRET_KEY, ALGORITHM
                
                token = auth_header.split(" ")[1]
                payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
                return payload.get("sub")
            except:
                pass
        
        return None
    
    def _rate_limit_response(self, request: Request) -> JSONResponse:
        """Return rate limit exceeded response"""
        logger.warning(f"Rate limit exceeded for {self._get_client_ip(request)}")
        
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "error": "Rate limit exceeded",
                "message": f"Too many requests. Limit: {self.calls} per {self.period} seconds",
                "retry_after": self.period
            },
            headers={"Retry-After": str(self.period)}
        )
    
    async def _cleanup_old_requests(self):
        """Periodic cleanup of old request records"""
        while True:
            try:
                await asyncio.sleep(60)  # Cleanup every minute
                current_time = time.time()
                cutoff_time = current_time - self.period
                
                # Cleanup IP requests
                for ip, requests in list(self.ip_requests.items()):
                    while requests and requests[0] < cutoff_time:
                        requests.popleft()
                    if not requests:
                        del self.ip_requests[ip]
                
                # Cleanup user requests
                for user_id, requests in list(self.user_requests.items()):
                    while requests and requests[0] < cutoff_time:
                        requests.popleft()
                    if not requests:
                        del self.user_requests[user_id]
                        
            except Exception as e:
                logger.error(f"Rate limit cleanup error: {e}")


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Add security headers to responses"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        
        self.security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' https:; "
                "connect-src 'self' https:; "
                "frame-ancestors 'none';"
            )
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Add security headers
        for header, value in self.security_headers.items():
            response.headers[header] = value
        
        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Log HTTP requests and responses"""
    
    def __init__(self, app: ASGIApp, log_body: bool = False):
        super().__init__(app)
        self.log_body = log_body
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # Log request
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("User-Agent", "Unknown")
        
        request_log = {
            "method": request.method,
            "url": str(request.url),
            "client_ip": client_ip,
            "user_agent": user_agent,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        if self.log_body and request.method in ["POST", "PUT", "PATCH"]:
            try:
                body = await request.body()
                if body:
                    request_log["body_size"] = len(body)
            except:
                pass
        
        logger.info(f"Request: {json.dumps(request_log)}")
        
        # Process request
        try:
            response = await call_next(request)
            
            # Log response
            process_time = time.time() - start_time
            
            response_log = {
                "status_code": response.status_code,
                "process_time": round(process_time, 4),
                "response_size": response.headers.get("content-length", "unknown")
            }
            
            logger.info(f"Response: {json.dumps(response_log)}")
            
            # Add process time header
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            
            error_log = {
                "error": str(e),
                "process_time": round(process_time, 4),
                "status": "error"
            }
            
            logger.error(f"Request error: {json.dumps(error_log)}")
            raise
    
    def _get_client_ip(self, request: Request) -> Optional[str]:
        """Get client IP address"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else None


class CORSMiddleware(BaseHTTPMiddleware):
    """Custom CORS middleware with enhanced security"""
    
    def __init__(
        self,
        app: ASGIApp,
        allowed_origins: list = None,
        allowed_methods: list = None,
        allowed_headers: list = None,
        allow_credentials: bool = True,
        max_age: int = 86400
    ):
        super().__init__(app)
        self.allowed_origins = allowed_origins or ["http://localhost:3000", "http://localhost:5173"]
        self.allowed_methods = allowed_methods or ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"]
        self.allowed_headers = allowed_headers or [
            "Accept", "Accept-Language", "Content-Language", "Content-Type",
            "Authorization", "X-Requested-With", "X-API-Key"
        ]
        self.allow_credentials = allow_credentials
        self.max_age = max_age
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        origin = request.headers.get("Origin")
        
        # Handle preflight requests
        if request.method == "OPTIONS":
            response = Response()
            
            if origin and self._is_origin_allowed(origin):
                response.headers["Access-Control-Allow-Origin"] = origin
                response.headers["Access-Control-Allow-Methods"] = ", ".join(self.allowed_methods)
                response.headers["Access-Control-Allow-Headers"] = ", ".join(self.allowed_headers)
                response.headers["Access-Control-Max-Age"] = str(self.max_age)
                
                if self.allow_credentials:
                    response.headers["Access-Control-Allow-Credentials"] = "true"
            
            return response
        
        # Process actual request
        response = await call_next(request)
        
        # Add CORS headers to response
        if origin and self._is_origin_allowed(origin):
            response.headers["Access-Control-Allow-Origin"] = origin
            
            if self.allow_credentials:
                response.headers["Access-Control-Allow-Credentials"] = "true"
        
        return response
    
    def _is_origin_allowed(self, origin: str) -> bool:
        """Check if origin is allowed"""
        if "*" in self.allowed_origins:
            return True
        
        return origin in self.allowed_origins


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Global error handling middleware"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            return await call_next(request)
        except HTTPException:
            # Re-raise HTTP exceptions (they're handled by FastAPI)
            raise
        except Exception as e:
            # Log unexpected errors
            logger.error(f"Unhandled error in {request.method} {request.url}: {str(e)}", exc_info=True)
            
            # Return generic error response
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "error": "Internal server error",
                    "message": "An unexpected error occurred. Please try again later.",
                    "request_id": getattr(request.state, "request_id", "unknown")
                }
            )


# Utility functions for middleware configuration
def setup_middleware(app):
    """Setup all middleware for the application"""
    
    # Error handling (should be first)
    app.add_middleware(ErrorHandlingMiddleware)
    
    # Security headers
    app.add_middleware(SecurityHeadersMiddleware)
    
    # CORS
    app.add_middleware(CORSMiddleware)
    
    # Request logging
    app.add_middleware(RequestLoggingMiddleware, log_body=False)
    
    # Rate limiting
    app.add_middleware(
        RateLimitMiddleware,
        calls=1000,  # 1000 requests
        period=3600,  # per hour
        per_ip=True,
        exclude_paths=["/health", "/metrics"]
    )
    
    return app
