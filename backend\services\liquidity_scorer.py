"""
Liquidity Scoring Service
Advanced liquidity assessment for bonds using multiple factors and ML models
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta, date
import asyncio
import logging
from decimal import Decimal

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor

from app.models.bond import Bond, BondPrice, LiquidityTier
from app.models.trade import Trade, TradeExecution

logger = logging.getLogger(__name__)


@dataclass
class LiquidityMetrics:
    """Comprehensive liquidity metrics for a bond"""
    bond_id: str
    cusip: str
    
    # Volume metrics
    avg_daily_volume: float
    volume_volatility: float
    volume_trend: float
    
    # Spread metrics
    bid_ask_spread: float
    spread_volatility: float
    
    # Market depth
    market_depth_score: float
    
    # Trading frequency
    trading_frequency: float
    days_since_last_trade: int
    
    # Price impact
    price_impact_score: float
    
    # Overall liquidity score
    liquidity_score: float
    liquidity_tier: str
    confidence_level: float


class LiquidityNeuralNetwork(nn.Module):
    """Neural network for liquidity scoring"""
    
    def __init__(self, input_dim: int = 20):
        super().__init__()
        
        self.network = nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 1),
            nn.Sigmoid()  # Output between 0 and 1
        )
    
    def forward(self, x):
        return self.network(x) * 100  # Scale to 0-100


class LiquidityScorer:
    """Main liquidity scoring engine"""
    
    def __init__(self):
        self.neural_network = LiquidityNeuralNetwork()
        self.random_forest = RandomForestRegressor(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        self.is_initialized = False
        
        # Liquidity factors and weights
        self.factor_weights = {
            'volume': 0.25,
            'spread': 0.20,
            'frequency': 0.15,
            'depth': 0.15,
            'impact': 0.10,
            'volatility': 0.10,
            'structural': 0.05
        }
        
        # Benchmark values for normalization
        self.benchmarks = {
            'high_volume_threshold': 10000000,  # $10M daily volume
            'low_spread_threshold': 0.25,       # 25 bps
            'high_frequency_threshold': 0.8,    # 80% of days traded
        }
    
    async def initialize(self):
        """Initialize the liquidity scorer"""
        try:
            logger.info("Initializing Liquidity Scorer...")
            
            # Load pre-trained models if available
            try:
                checkpoint = torch.load("models/liquidity_model.pth", map_location='cpu')
                self.neural_network.load_state_dict(checkpoint['model_state_dict'])
                logger.info("Loaded pre-trained liquidity neural network")
            except FileNotFoundError:
                logger.info("No pre-trained liquidity model found, using random initialization")
            
            self.is_initialized = True
            logger.info("✅ Liquidity Scorer initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Liquidity Scorer: {e}")
            raise
    
    async def calculate_liquidity_score(self, bond_id: str, db: Session) -> float:
        """Calculate comprehensive liquidity score for a bond"""
        if not self.is_initialized:
            await self.initialize()
        
        # Get bond data
        bond = db.query(Bond).filter(Bond.id == bond_id).first()
        if not bond:
            raise ValueError(f"Bond with ID {bond_id} not found")
        
        # Calculate individual liquidity metrics
        metrics = await self._calculate_liquidity_metrics(bond, db)
        
        # Extract features for ML models
        features = self._extract_liquidity_features(bond, metrics)
        
        # Get scores from different models
        nn_score = self._get_neural_network_score(features)
        rf_score = self._get_random_forest_score(features)
        rule_score = self._get_rule_based_score(metrics)
        
        # Weighted combination of scores
        final_score = (
            0.4 * nn_score +
            0.3 * rf_score +
            0.3 * rule_score
        )
        
        # Ensure score is within bounds
        final_score = max(0, min(100, final_score))
        
        # Update bond's liquidity score
        bond.liquidity_score = final_score
        bond.liquidity_tier = self._determine_liquidity_tier(final_score)
        
        return final_score
    
    async def _calculate_liquidity_metrics(self, bond: Bond, db: Session) -> LiquidityMetrics:
        """Calculate detailed liquidity metrics"""
        
        # Volume metrics
        volume_metrics = await self._calculate_volume_metrics(bond, db)
        
        # Spread metrics
        spread_metrics = await self._calculate_spread_metrics(bond, db)
        
        # Trading frequency
        frequency_metrics = await self._calculate_frequency_metrics(bond, db)
        
        # Market depth (simplified)
        depth_score = self._calculate_market_depth_score(bond)
        
        # Price impact
        impact_score = await self._calculate_price_impact_score(bond, db)
        
        return LiquidityMetrics(
            bond_id=str(bond.id),
            cusip=bond.cusip,
            avg_daily_volume=volume_metrics['avg_volume'],
            volume_volatility=volume_metrics['volatility'],
            volume_trend=volume_metrics['trend'],
            bid_ask_spread=spread_metrics['avg_spread'],
            spread_volatility=spread_metrics['volatility'],
            market_depth_score=depth_score,
            trading_frequency=frequency_metrics['frequency'],
            days_since_last_trade=frequency_metrics['days_since_last'],
            price_impact_score=impact_score,
            liquidity_score=0,  # Will be calculated later
            liquidity_tier="",  # Will be determined later
            confidence_level=0.8  # Default confidence
        )
    
    async def _calculate_volume_metrics(self, bond: Bond, db: Session) -> Dict[str, float]:
        """Calculate volume-based liquidity metrics"""
        # Get recent price/volume data (last 30 days)
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=30)
        
        prices = db.query(BondPrice).filter(
            BondPrice.bond_id == bond.id,
            BondPrice.price_date >= start_date,
            BondPrice.price_date <= end_date,
            BondPrice.volume.isnot(None)
        ).all()
        
        if not prices:
            return {'avg_volume': 0, 'volatility': 0, 'trend': 0}
        
        volumes = [float(p.volume) for p in prices if p.volume]
        
        if not volumes:
            return {'avg_volume': 0, 'volatility': 0, 'trend': 0}
        
        avg_volume = np.mean(volumes)
        volatility = np.std(volumes) / avg_volume if avg_volume > 0 else 0
        
        # Calculate trend (simple linear regression slope)
        if len(volumes) > 1:
            x = np.arange(len(volumes))
            trend = np.polyfit(x, volumes, 1)[0]  # Slope
        else:
            trend = 0
        
        return {
            'avg_volume': avg_volume,
            'volatility': volatility,
            'trend': trend
        }
    
    async def _calculate_spread_metrics(self, bond: Bond, db: Session) -> Dict[str, float]:
        """Calculate spread-based liquidity metrics"""
        # Get recent spread data
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=30)
        
        prices = db.query(BondPrice).filter(
            BondPrice.bond_id == bond.id,
            BondPrice.price_date >= start_date,
            BondPrice.price_date <= end_date,
            BondPrice.bid_ask_spread.isnot(None)
        ).all()
        
        if not prices:
            # Use bond's current spread if available
            current_spread = float(bond.bid_ask_spread or 0.5)  # Default 50 bps
            return {'avg_spread': current_spread, 'volatility': 0}
        
        spreads = [float(p.bid_ask_spread) for p in prices if p.bid_ask_spread]
        
        if not spreads:
            current_spread = float(bond.bid_ask_spread or 0.5)
            return {'avg_spread': current_spread, 'volatility': 0}
        
        avg_spread = np.mean(spreads)
        volatility = np.std(spreads) / avg_spread if avg_spread > 0 else 0
        
        return {
            'avg_spread': avg_spread,
            'volatility': volatility
        }
    
    async def _calculate_frequency_metrics(self, bond: Bond, db: Session) -> Dict[str, Any]:
        """Calculate trading frequency metrics"""
        # Get trading data for last 90 days
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=90)
        
        trades = db.query(Trade).filter(
            Trade.bond_id == bond.id,
            Trade.trade_date >= start_date,
            Trade.trade_date <= end_date
        ).all()
        
        total_days = 90
        trading_days = len(set(trade.trade_date.date() for trade in trades))
        frequency = trading_days / total_days
        
        # Days since last trade
        if trades:
            last_trade_date = max(trade.trade_date.date() for trade in trades)
            days_since_last = (end_date - last_trade_date).days
        else:
            days_since_last = 999  # No recent trades
        
        return {
            'frequency': frequency,
            'days_since_last': days_since_last
        }
    
    def _calculate_market_depth_score(self, bond: Bond) -> float:
        """Calculate market depth score (simplified)"""
        # This is a simplified calculation
        # In practice, you'd use order book data
        
        factors = []
        
        # Issue size factor
        face_value = float(bond.face_value or 1000)
        if hasattr(bond, 'issue_size'):
            issue_size = getattr(bond, 'issue_size', 100000000)  # Default $100M
            size_factor = min(issue_size / 1000000000, 1.0)  # Normalize to $1B
            factors.append(size_factor)
        
        # Issuer reputation (based on sector and type)
        if bond.bond_type in ['treasury', 'government']:
            factors.append(1.0)
        elif bond.bond_type == 'corporate' and bond.sector == 'financial':
            factors.append(0.8)
        else:
            factors.append(0.6)
        
        # Time to maturity factor
        if bond.maturity_date:
            years_to_maturity = (bond.maturity_date - date.today()).days / 365.25
            maturity_factor = 1.0 if 2 <= years_to_maturity <= 10 else 0.7
            factors.append(maturity_factor)
        
        return np.mean(factors) if factors else 0.5
    
    async def _calculate_price_impact_score(self, bond: Bond, db: Session) -> float:
        """Calculate price impact score"""
        # Get recent large trades
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=30)
        
        executions = db.query(TradeExecution).join(Trade).filter(
            Trade.bond_id == bond.id,
            TradeExecution.execution_time >= start_date,
            TradeExecution.quantity > 1000000  # Large trades > $1M
        ).all()
        
        if not executions:
            return 0.8  # Default good score if no large trades
        
        # Simplified price impact calculation
        # In practice, you'd analyze price movement around trade times
        avg_trade_size = np.mean([float(ex.quantity) for ex in executions])
        
        # Larger average trade size suggests better liquidity
        impact_score = min(avg_trade_size / 10000000, 1.0)  # Normalize to $10M
        
        return impact_score

    def _extract_liquidity_features(self, bond: Bond, metrics: LiquidityMetrics) -> np.ndarray:
        """Extract features for ML models"""
        features = []

        # Volume features
        features.extend([
            np.log1p(metrics.avg_daily_volume) / 20,  # Log-normalized volume
            metrics.volume_volatility,
            metrics.volume_trend / 1000000,  # Normalized trend
        ])

        # Spread features
        features.extend([
            metrics.bid_ask_spread / 2.0,  # Normalize to 200 bps max
            metrics.spread_volatility,
        ])

        # Frequency features
        features.extend([
            metrics.trading_frequency,
            min(metrics.days_since_last_trade / 30, 1.0),  # Normalize to 30 days
        ])

        # Market structure features
        features.extend([
            metrics.market_depth_score,
            metrics.price_impact_score,
        ])

        # Bond characteristics
        features.extend([
            float(bond.duration or 0) / 20,  # Normalize to 20 years
            float(bond.yield_to_maturity or 0) / 20,  # Normalize to 20%
            float(bond.current_price or 100) / 200,  # Normalize around par
        ])

        # Time to maturity
        if bond.maturity_date:
            years_to_maturity = (bond.maturity_date - date.today()).days / 365.25
            features.append(min(years_to_maturity / 30, 1.0))
        else:
            features.append(0.5)

        # Bond type encoding
        type_encoding = [0] * 5
        type_map = {'corporate': 0, 'government': 1, 'municipal': 2, 'treasury': 3, 'agency': 4}
        bond_type = bond.bond_type or 'corporate'
        if bond_type in type_map:
            type_encoding[type_map[bond_type]] = 1
        features.extend(type_encoding)

        # Pad to fixed size
        while len(features) < 20:
            features.append(0.0)

        return np.array(features[:20], dtype=np.float32)

    def _get_neural_network_score(self, features: np.ndarray) -> float:
        """Get liquidity score from neural network"""
        try:
            with torch.no_grad():
                feature_tensor = torch.tensor(features).unsqueeze(0)
                score = self.neural_network(feature_tensor).item()
            return score
        except Exception as e:
            logger.warning(f"Neural network scoring failed: {e}")
            return 50.0  # Default neutral score

    def _get_random_forest_score(self, features: np.ndarray) -> float:
        """Get liquidity score from random forest (placeholder)"""
        # This would use a pre-trained random forest model
        # For now, return a simple heuristic score

        volume_score = min(features[0] * 100, 100)  # Volume-based score
        spread_score = max(0, 100 - features[3] * 100)  # Lower spread = higher score
        frequency_score = features[5] * 100  # Frequency-based score

        rf_score = (volume_score + spread_score + frequency_score) / 3
        return rf_score

    def _get_rule_based_score(self, metrics: LiquidityMetrics) -> float:
        """Get liquidity score using rule-based approach"""
        score = 0

        # Volume component (25 points max)
        if metrics.avg_daily_volume >= self.benchmarks['high_volume_threshold']:
            score += 25
        elif metrics.avg_daily_volume >= self.benchmarks['high_volume_threshold'] / 2:
            score += 20
        elif metrics.avg_daily_volume >= self.benchmarks['high_volume_threshold'] / 5:
            score += 15
        elif metrics.avg_daily_volume >= self.benchmarks['high_volume_threshold'] / 10:
            score += 10
        else:
            score += 5

        # Spread component (20 points max)
        if metrics.bid_ask_spread <= self.benchmarks['low_spread_threshold']:
            score += 20
        elif metrics.bid_ask_spread <= self.benchmarks['low_spread_threshold'] * 2:
            score += 15
        elif metrics.bid_ask_spread <= self.benchmarks['low_spread_threshold'] * 4:
            score += 10
        else:
            score += 5

        # Frequency component (15 points max)
        if metrics.trading_frequency >= self.benchmarks['high_frequency_threshold']:
            score += 15
        elif metrics.trading_frequency >= 0.6:
            score += 12
        elif metrics.trading_frequency >= 0.4:
            score += 8
        elif metrics.trading_frequency >= 0.2:
            score += 5
        else:
            score += 2

        # Market depth component (15 points max)
        score += metrics.market_depth_score * 15

        # Price impact component (10 points max)
        score += metrics.price_impact_score * 10

        # Volatility penalty (10 points max)
        volatility_penalty = min(metrics.volume_volatility * 10, 10)
        score += (10 - volatility_penalty)

        # Recent trading bonus/penalty (5 points max)
        if metrics.days_since_last_trade <= 1:
            score += 5
        elif metrics.days_since_last_trade <= 7:
            score += 3
        elif metrics.days_since_last_trade <= 30:
            score += 1
        else:
            score -= 2  # Penalty for stale trading

        return max(0, min(100, score))

    def _determine_liquidity_tier(self, score: float) -> str:
        """Determine liquidity tier based on score"""
        if score >= 80:
            return LiquidityTier.TIER_1.value
        elif score >= 60:
            return LiquidityTier.TIER_2.value
        elif score >= 40:
            return LiquidityTier.TIER_3.value
        else:
            return LiquidityTier.TIER_4.value

    async def calculate_portfolio_liquidity(self, portfolio_id: str, db: Session) -> Dict[str, Any]:
        """Calculate overall portfolio liquidity metrics"""
        from app.models.portfolio import PortfolioHolding

        holdings = db.query(PortfolioHolding).filter(
            PortfolioHolding.portfolio_id == portfolio_id,
            PortfolioHolding.is_active == True
        ).all()

        if not holdings:
            return {'liquidity_score': 0, 'tier_distribution': {}}

        total_value = sum(float(h.market_value or 0) for h in holdings)
        if total_value == 0:
            return {'liquidity_score': 0, 'tier_distribution': {}}

        weighted_score = 0
        tier_distribution = {tier.value: 0 for tier in LiquidityTier}

        for holding in holdings:
            bond_score = await self.calculate_liquidity_score(str(holding.bond_id), db)
            weight = float(holding.market_value or 0) / total_value
            weighted_score += bond_score * weight

            # Update tier distribution
            tier = self._determine_liquidity_tier(bond_score)
            tier_distribution[tier] += weight * 100

        return {
            'liquidity_score': weighted_score,
            'tier_distribution': tier_distribution,
            'total_holdings': len(holdings)
        }

    async def get_liquidity_metrics(self, bond_id: str, db: Session) -> LiquidityMetrics:
        """Get detailed liquidity metrics for a bond"""
        if not self.is_initialized:
            await self.initialize()

        bond = db.query(Bond).filter(Bond.id == bond_id).first()
        if not bond:
            raise ValueError(f"Bond with ID {bond_id} not found")

        metrics = await self._calculate_liquidity_metrics(bond, db)

        # Calculate overall score
        liquidity_score = await self.calculate_liquidity_score(bond_id, db)
        metrics.liquidity_score = liquidity_score
        metrics.liquidity_tier = self._determine_liquidity_tier(liquidity_score)

        return metrics

    def save_model(self, path: str):
        """Save liquidity scoring model"""
        torch.save({
            'model_state_dict': self.neural_network.state_dict(),
            'scaler': self.scaler,
            'factor_weights': self.factor_weights,
            'benchmarks': self.benchmarks
        }, path)
