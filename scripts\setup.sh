#!/bin/bash

# LiquidBond Setup Script
# This script sets up the development environment for LiquidBond

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Python version
check_python_version() {
    if command_exists python3; then
        python_version=$(python3 --version | cut -d' ' -f2)
        required_version="3.9"
        
        if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" = "$required_version" ]; then
            print_success "Python $python_version is installed"
            return 0
        else
            print_error "Python $required_version or higher is required. Found: $python_version"
            return 1
        fi
    else
        print_error "Python 3 is not installed"
        return 1
    fi
}

# Function to check Node.js version
check_node_version() {
    if command_exists node; then
        node_version=$(node --version | sed 's/v//')
        required_version="18.0.0"
        
        if [ "$(printf '%s\n' "$required_version" "$node_version" | sort -V | head -n1)" = "$required_version" ]; then
            print_success "Node.js $node_version is installed"
            return 0
        else
            print_error "Node.js $required_version or higher is required. Found: $node_version"
            return 1
        fi
    else
        print_error "Node.js is not installed"
        return 1
    fi
}

# Function to setup Python virtual environment
setup_python_env() {
    print_status "Setting up Python virtual environment..."
    
    cd backend
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_success "Virtual environment created"
    else
        print_warning "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install dependencies
    print_status "Installing Python dependencies..."
    pip install -r requirements.txt
    
    print_success "Python environment setup complete"
    cd ..
}

# Function to setup Node.js environment
setup_node_env() {
    print_status "Setting up Node.js environment..."
    
    cd frontend
    
    # Install dependencies
    print_status "Installing Node.js dependencies..."
    npm install
    
    print_success "Node.js environment setup complete"
    cd ..
}

# Function to setup environment files
setup_env_files() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        cp .env.example backend/.env
        print_success "Backend .env file created from template"
        print_warning "Please update backend/.env with your configuration"
    else
        print_warning "Backend .env file already exists"
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env" ]; then
        cat > frontend/.env << EOF
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/ws
VITE_APP_NAME=LiquidBond
VITE_APP_VERSION=1.0.0
VITE_ENABLE_QUANTUM_ANALYSIS=true
VITE_ENABLE_REAL_TIME_DATA=true
VITE_ENABLE_PAPER_TRADING=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_MOCK_DATA=false
EOF
        print_success "Frontend .env file created"
    else
        print_warning "Frontend .env file already exists"
    fi
}

# Function to setup databases with Docker
setup_databases() {
    print_status "Setting up databases with Docker..."
    
    if command_exists docker && command_exists docker-compose; then
        cd docker
        
        # Start only the database services
        docker-compose up -d postgres redis mongodb influxdb
        
        print_status "Waiting for databases to be ready..."
        sleep 30
        
        # Check if databases are healthy
        if docker-compose ps | grep -q "healthy"; then
            print_success "Databases are running and healthy"
        else
            print_warning "Some databases may not be ready yet. Check with: docker-compose ps"
        fi
        
        cd ..
    else
        print_warning "Docker or docker-compose not found. Please install Docker to use containerized databases."
        print_status "Alternatively, install PostgreSQL, Redis, MongoDB, and InfluxDB manually."
    fi
}

# Function to run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    cd backend
    source venv/bin/activate
    
    # Run Alembic migrations
    if command_exists alembic; then
        alembic upgrade head
        print_success "Database migrations completed"
    else
        print_warning "Alembic not found. Install with: pip install alembic"
    fi
    
    cd ..
}

# Function to load sample data
load_sample_data() {
    print_status "Loading sample data..."
    
    cd backend
    source venv/bin/activate
    
    # Run sample data script
    if [ -f "scripts/load_sample_data.py" ]; then
        python scripts/load_sample_data.py
        print_success "Sample data loaded"
    else
        print_warning "Sample data script not found"
    fi
    
    cd ..
}

# Function to setup pre-commit hooks
setup_pre_commit() {
    print_status "Setting up pre-commit hooks..."
    
    cd backend
    source venv/bin/activate
    
    if command_exists pre-commit; then
        pre-commit install
        print_success "Pre-commit hooks installed"
    else
        print_warning "pre-commit not found. Install with: pip install pre-commit"
    fi
    
    cd ..
}

# Function to create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    directories=(
        "backend/logs"
        "backend/ml_models/model_artifacts"
        "backend/ml_models/training_data"
        "data/market_data"
        "data/schemas"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_success "Created directory: $dir"
        fi
    done
}

# Main setup function
main() {
    print_status "Starting LiquidBond setup..."
    echo
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    check_python_version || exit 1
    check_node_version || exit 1
    
    # Create directories
    create_directories
    
    # Setup environment files
    setup_env_files
    
    # Setup Python environment
    setup_python_env
    
    # Setup Node.js environment
    setup_node_env
    
    # Setup databases
    setup_databases
    
    # Run migrations
    run_migrations
    
    # Load sample data
    load_sample_data
    
    # Setup pre-commit hooks
    setup_pre_commit
    
    echo
    print_success "LiquidBond setup completed successfully!"
    echo
    print_status "Next steps:"
    echo "1. Update backend/.env with your API keys and configuration"
    echo "2. Start the backend: cd backend && source venv/bin/activate && uvicorn app.main:app --reload"
    echo "3. Start the frontend: cd frontend && npm run dev"
    echo "4. Visit http://localhost:3000 to access the application"
    echo
    print_status "For Docker deployment, run: cd docker && docker-compose up"
}

# Run main function
main "$@"
