"""
Market Data Service
Real-time and historical market data integration for bonds
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta, date
from dataclasses import dataclass
import json
import os
from decimal import Decimal

import pandas as pd
import numpy as np
from sqlalchemy.orm import Session

from app.models.bond import Bond, BondPrice
from app.config import settings

logger = logging.getLogger(__name__)


@dataclass
class MarketDataPoint:
    """Single market data point"""
    symbol: str
    cusip: str
    price: float
    yield_value: Optional[float]
    bid_price: Optional[float]
    ask_price: Optional[float]
    volume: Optional[float]
    timestamp: datetime
    source: str


@dataclass
class EconomicIndicator:
    """Economic indicator data"""
    indicator: str
    value: float
    date: date
    source: str


class MarketDataService:
    """Market data integration service"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.is_initialized = False
        
        # API configurations
        self.apis = {
            'fred': {
                'base_url': 'https://api.stlouisfed.org/fred',
                'api_key': os.getenv('FRED_API_KEY', ''),
                'rate_limit': 120  # requests per minute
            },
            'yahoo': {
                'base_url': 'https://query1.finance.yahoo.com/v8/finance',
                'rate_limit': 2000  # requests per hour
            },
            'alpha_vantage': {
                'base_url': 'https://www.alphavantage.co/query',
                'api_key': os.getenv('ALPHA_VANTAGE_API_KEY', ''),
                'rate_limit': 5  # requests per minute for free tier
            }
        }
        
        # Rate limiting
        self.last_request_times = {api: [] for api in self.apis.keys()}
        
        # Cache for market data
        self.price_cache = {}
        self.cache_ttl = 300  # 5 minutes
        
        # Economic indicators to track
        self.economic_indicators = [
            'DGS10',    # 10-Year Treasury Rate
            'DGS2',     # 2-Year Treasury Rate
            'DGS30',    # 30-Year Treasury Rate
            'DFEDTARU', # Federal Funds Rate
            'CPIAUCSL', # Consumer Price Index
            'UNRATE',   # Unemployment Rate
        ]
    
    async def initialize(self):
        """Initialize market data service"""
        try:
            logger.info("Initializing Market Data Service...")
            
            # Create HTTP session
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            # Test API connections
            await self._test_api_connections()
            
            self.is_initialized = True
            logger.info("✅ Market Data Service initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Market Data Service: {e}")
            raise
    
    async def close(self):
        """Close HTTP session"""
        if self.session:
            await self.session.close()
    
    async def _test_api_connections(self):
        """Test connections to external APIs"""
        # Test FRED API
        if self.apis['fred']['api_key']:
            try:
                await self._fetch_fred_data('DGS10', limit=1)
                logger.info("✅ FRED API connection successful")
            except Exception as e:
                logger.warning(f"⚠️ FRED API connection failed: {e}")
        
        # Test Yahoo Finance (no API key required)
        try:
            await self._fetch_yahoo_data('TLT')  # iShares 20+ Year Treasury Bond ETF
            logger.info("✅ Yahoo Finance API connection successful")
        except Exception as e:
            logger.warning(f"⚠️ Yahoo Finance API connection failed: {e}")
    
    async def get_bond_price(self, cusip: str, symbol: Optional[str] = None) -> Optional[MarketDataPoint]:
        """Get current bond price"""
        if not self.is_initialized:
            await self.initialize()
        
        # Check cache first
        cache_key = cusip
        if cache_key in self.price_cache:
            cached_data, timestamp = self.price_cache[cache_key]
            if (datetime.utcnow() - timestamp).seconds < self.cache_ttl:
                return cached_data
        
        # Try different data sources
        market_data = None
        
        # Try Yahoo Finance first (if symbol available)
        if symbol:
            try:
                market_data = await self._fetch_yahoo_bond_data(symbol, cusip)
            except Exception as e:
                logger.debug(f"Yahoo Finance failed for {cusip}: {e}")
        
        # Try Alpha Vantage as fallback
        if not market_data and self.apis['alpha_vantage']['api_key']:
            try:
                market_data = await self._fetch_alpha_vantage_data(cusip)
            except Exception as e:
                logger.debug(f"Alpha Vantage failed for {cusip}: {e}")
        
        # Cache the result
        if market_data:
            self.price_cache[cache_key] = (market_data, datetime.utcnow())
        
        return market_data
    
    async def _fetch_yahoo_bond_data(self, symbol: str, cusip: str) -> Optional[MarketDataPoint]:
        """Fetch bond data from Yahoo Finance"""
        if not await self._check_rate_limit('yahoo'):
            await asyncio.sleep(1)
        
        url = f"{self.apis['yahoo']['base_url']}/chart/{symbol}"
        params = {
            'interval': '1d',
            'range': '1d'
        }
        
        async with self.session.get(url, params=params) as response:
            if response.status == 200:
                data = await response.json()
                
                if 'chart' in data and data['chart']['result']:
                    result = data['chart']['result'][0]
                    meta = result['meta']
                    
                    return MarketDataPoint(
                        symbol=symbol,
                        cusip=cusip,
                        price=meta.get('regularMarketPrice', 0),
                        yield_value=None,  # Not available from Yahoo
                        bid_price=meta.get('bid'),
                        ask_price=meta.get('ask'),
                        volume=meta.get('regularMarketVolume'),
                        timestamp=datetime.utcnow(),
                        source='yahoo'
                    )
        
        return None
    
    async def _fetch_alpha_vantage_data(self, cusip: str) -> Optional[MarketDataPoint]:
        """Fetch bond data from Alpha Vantage"""
        if not await self._check_rate_limit('alpha_vantage'):
            await asyncio.sleep(12)  # Wait for rate limit reset
        
        # Alpha Vantage doesn't have direct bond data, so this is a placeholder
        # In practice, you'd use a specialized bond data provider
        return None
    
    async def get_treasury_rates(self) -> Dict[str, float]:
        """Get current treasury rates from FRED"""
        if not self.is_initialized:
            await self.initialize()
        
        rates = {}
        
        if not self.apis['fred']['api_key']:
            logger.warning("FRED API key not configured")
            return rates
        
        treasury_series = {
            '2Y': 'DGS2',
            '5Y': 'DGS5',
            '10Y': 'DGS10',
            '30Y': 'DGS30'
        }
        
        for maturity, series_id in treasury_series.items():
            try:
                data = await self._fetch_fred_data(series_id, limit=1)
                if data:
                    rates[maturity] = float(data[0]['value'])
            except Exception as e:
                logger.warning(f"Failed to fetch {maturity} treasury rate: {e}")
        
        return rates
    
    async def _fetch_fred_data(self, series_id: str, limit: int = 100) -> List[Dict]:
        """Fetch data from FRED API"""
        if not await self._check_rate_limit('fred'):
            await asyncio.sleep(1)
        
        url = f"{self.apis['fred']['base_url']}/series/observations"
        params = {
            'series_id': series_id,
            'api_key': self.apis['fred']['api_key'],
            'file_type': 'json',
            'limit': limit,
            'sort_order': 'desc'
        }
        
        async with self.session.get(url, params=params) as response:
            if response.status == 200:
                data = await response.json()
                return data.get('observations', [])
            else:
                raise Exception(f"FRED API error: {response.status}")
    
    async def _fetch_yahoo_data(self, symbol: str) -> Dict:
        """Fetch data from Yahoo Finance"""
        if not await self._check_rate_limit('yahoo'):
            await asyncio.sleep(1)
        
        url = f"{self.apis['yahoo']['base_url']}/chart/{symbol}"
        params = {'interval': '1d', 'range': '1d'}
        
        async with self.session.get(url, params=params) as response:
            if response.status == 200:
                return await response.json()
            else:
                raise Exception(f"Yahoo Finance API error: {response.status}")
    
    async def _check_rate_limit(self, api: str) -> bool:
        """Check if API rate limit allows request"""
        now = datetime.utcnow()
        api_config = self.apis[api]
        rate_limit = api_config['rate_limit']
        
        # Clean old timestamps
        cutoff_time = now - timedelta(minutes=1)
        self.last_request_times[api] = [
            t for t in self.last_request_times[api] if t > cutoff_time
        ]
        
        # Check if under rate limit
        if len(self.last_request_times[api]) < rate_limit:
            self.last_request_times[api].append(now)
            return True
        
        return False
    
    async def update_bond_prices(self, bonds: List[Bond], db: Session):
        """Update prices for multiple bonds"""
        updated_count = 0
        
        for bond in bonds:
            try:
                market_data = await self.get_bond_price(bond.cusip, bond.symbol)
                
                if market_data:
                    # Update bond current price
                    bond.current_price = Decimal(str(market_data.price))
                    
                    # Create price history record
                    price_record = BondPrice(
                        bond_id=bond.id,
                        price_date=market_data.timestamp.date(),
                        price=Decimal(str(market_data.price)),
                        yield_value=Decimal(str(market_data.yield_value)) if market_data.yield_value else None,
                        volume=Decimal(str(market_data.volume)) if market_data.volume else None,
                        bid_price=Decimal(str(market_data.bid_price)) if market_data.bid_price else None,
                        ask_price=Decimal(str(market_data.ask_price)) if market_data.ask_price else None,
                        bid_ask_spread=Decimal(str(market_data.ask_price - market_data.bid_price)) if market_data.bid_price and market_data.ask_price else None,
                        data_source=market_data.source
                    )
                    
                    db.add(price_record)
                    updated_count += 1
                
                # Small delay to respect rate limits
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Failed to update price for bond {bond.cusip}: {e}")
                continue
        
        db.commit()
        logger.info(f"Updated prices for {updated_count} bonds")
        
        return updated_count
    
    async def get_economic_indicators(self) -> Dict[str, EconomicIndicator]:
        """Get current economic indicators"""
        if not self.is_initialized:
            await self.initialize()
        
        indicators = {}
        
        if not self.apis['fred']['api_key']:
            logger.warning("FRED API key not configured")
            return indicators
        
        for indicator_id in self.economic_indicators:
            try:
                data = await self._fetch_fred_data(indicator_id, limit=1)
                if data and data[0]['value'] != '.':
                    indicators[indicator_id] = EconomicIndicator(
                        indicator=indicator_id,
                        value=float(data[0]['value']),
                        date=datetime.strptime(data[0]['date'], '%Y-%m-%d').date(),
                        source='fred'
                    )
            except Exception as e:
                logger.warning(f"Failed to fetch indicator {indicator_id}: {e}")
        
        return indicators
    
    async def get_yield_curve(self) -> Dict[str, float]:
        """Get current yield curve data"""
        treasury_rates = await self.get_treasury_rates()
        return treasury_rates
