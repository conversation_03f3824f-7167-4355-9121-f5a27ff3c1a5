"""
Configuration settings for LiquidBond application
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application
    APP_NAME: str = "LiquidBond"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # Server
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    
    # Database
    DATABASE_URL: str = Field(
        default="postgresql://user:password@localhost:5432/liquidbond",
        env="DATABASE_URL"
    )
    
    # Redis
    REDIS_URL: str = Field(
        default="redis://localhost:6379/0",
        env="REDIS_URL"
    )
    
    # MongoDB
    MONGODB_URL: str = Field(
        default="mongodb://localhost:27017/liquidbond",
        env="MONGODB_URL"
    )
    
    # InfluxDB
    INFLUXDB_URL: str = Field(
        default="http://localhost:8086",
        env="INFLUXDB_URL"
    )
    INFLUXDB_TOKEN: Optional[str] = Field(default=None, env="INFLUXDB_TOKEN")
    INFLUXDB_ORG: str = Field(default="liquidbond", env="INFLUXDB_ORG")
    INFLUXDB_BUCKET: str = Field(default="market_data", env="INFLUXDB_BUCKET")
    
    # Security
    SECRET_KEY: str = Field(
        default="your-secret-key-change-in-production",
        env="SECRET_KEY"
    )
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # External APIs
    ALPHA_VANTAGE_API_KEY: Optional[str] = Field(default=None, env="ALPHA_VANTAGE_API_KEY")
    FRED_API_KEY: Optional[str] = Field(default=None, env="FRED_API_KEY")
    QUANDL_API_KEY: Optional[str] = Field(default=None, env="QUANDL_API_KEY")
    
    # AI/ML Settings
    MODEL_PATH: str = Field(default="./ml_models", env="MODEL_PATH")
    BATCH_SIZE: int = Field(default=32, env="BATCH_SIZE")
    MAX_WORKERS: int = Field(default=4, env="MAX_WORKERS")
    
    # Trading
    ENABLE_PAPER_TRADING: bool = Field(default=True, env="ENABLE_PAPER_TRADING")
    MAX_POSITION_SIZE: float = Field(default=1000000.0, env="MAX_POSITION_SIZE")
    
    # Monitoring
    SENTRY_DSN: Optional[str] = Field(default=None, env="SENTRY_DSN")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    
    # CORS
    CORS_ORIGINS: list[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
        "https://liquidbond.ai"
    ]
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()


# Database configuration
class DatabaseConfig:
    """Database connection configuration"""
    
    @staticmethod
    def get_database_url() -> str:
        return settings.DATABASE_URL
    
    @staticmethod
    def get_redis_url() -> str:
        return settings.REDIS_URL
    
    @staticmethod
    def get_mongodb_url() -> str:
        return settings.MONGODB_URL


# API Keys configuration
class APIKeysConfig:
    """External API keys configuration"""
    
    @staticmethod
    def get_alpha_vantage_key() -> Optional[str]:
        return settings.ALPHA_VANTAGE_API_KEY
    
    @staticmethod
    def get_fred_key() -> Optional[str]:
        return settings.FRED_API_KEY
    
    @staticmethod
    def get_quandl_key() -> Optional[str]:
        return settings.QUANDL_API_KEY


# ML Model configuration
class MLConfig:
    """Machine Learning model configuration"""
    
    @staticmethod
    def get_model_path() -> str:
        return settings.MODEL_PATH
    
    @staticmethod
    def get_batch_size() -> int:
        return settings.BATCH_SIZE
    
    @staticmethod
    def get_max_workers() -> int:
        return settings.MAX_WORKERS
