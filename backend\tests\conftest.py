"""
Test configuration and fixtures for LiquidBond application
"""

import pytest
import asyncio
from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import Generator
import os
import tempfile

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from fastapi.testclient import TestClient

from app.database import Base, get_db
from app.main import app
from app.models.user import User, UserProfile, UserRole, UserStatus
from app.models.bond import Bond, BondPrice, BondRating, BondType, LiquidityTier
from app.models.portfolio import Portfolio, PortfolioHolding, PortfolioType, PortfolioStatus
from app.models.trade import Trade, TradeOrder, TradeExecution, OrderSide, OrderType, OrderStatus, TradeStatus


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test."""
    Base.metadata.create_all(bind=engine)
    
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session):
    """Create a test client with database dependency override."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture
def sample_user(db_session):
    """Create a sample user for testing."""
    user = User(
        email="<EMAIL>",
        username="testuser",
        first_name="Test",
        last_name="User",
        role=UserRole.PORTFOLIO_MANAGER.value,
        status=UserStatus.ACTIVE.value,
        is_active=True,
        is_verified=True
    )
    user.set_password("TestPassword123!")
    
    db_session.add(user)
    db_session.flush()
    
    # Create user profile
    profile = UserProfile(
        user_id=user.id,
        company="Test Company",
        job_title="Portfolio Manager",
        timezone="UTC",
        language="en",
        currency_preference="USD"
    )
    profile.generate_api_key()
    
    db_session.add(profile)
    db_session.commit()
    db_session.refresh(user)
    
    return user


@pytest.fixture
def admin_user(db_session):
    """Create an admin user for testing."""
    user = User(
        email="<EMAIL>",
        username="admin",
        first_name="Admin",
        last_name="User",
        role=UserRole.ADMIN.value,
        status=UserStatus.ACTIVE.value,
        is_active=True,
        is_verified=True
    )
    user.set_password("AdminPassword123!")
    
    db_session.add(user)
    db_session.flush()
    
    profile = UserProfile(
        user_id=user.id,
        company="LiquidBond",
        job_title="Administrator"
    )
    profile.generate_api_key()
    
    db_session.add(profile)
    db_session.commit()
    db_session.refresh(user)
    
    return user


@pytest.fixture
def sample_bond(db_session):
    """Create a sample bond for testing."""
    bond = Bond(
        cusip="12345678A",
        isin="US1234567890",
        symbol="TEST",
        issuer_name="Test Corporation",
        bond_type=BondType.CORPORATE.value,
        description="Test Corporate Bond",
        face_value=Decimal("1000.00"),
        coupon_rate=Decimal("5.25"),
        maturity_date=date.today() + timedelta(days=365*5),  # 5 years from now
        issue_date=date.today() - timedelta(days=30),
        current_price=Decimal("102.50"),
        yield_to_maturity=Decimal("4.85"),
        duration=Decimal("4.2"),
        convexity=Decimal("18.5"),
        liquidity_score=Decimal("75.0"),
        liquidity_tier=LiquidityTier.TIER_2.value,
        avg_daily_volume=Decimal("5000000.00"),
        bid_ask_spread=Decimal("0.25"),
        credit_spread=Decimal("150.0"),
        sector="Technology",
        industry="Software",
        country="USA",
        currency="USD",
        is_active=True
    )
    
    db_session.add(bond)
    db_session.commit()
    db_session.refresh(bond)
    
    return bond


@pytest.fixture
def sample_bond_with_rating(db_session, sample_bond):
    """Create a bond with credit rating."""
    rating = BondRating(
        bond_id=sample_bond.id,
        rating_agency="sp",
        rating="A",
        outlook="Stable",
        rating_date=date.today(),
        rating_action="New"
    )
    
    db_session.add(rating)
    db_session.commit()
    
    return sample_bond


@pytest.fixture
def sample_portfolio(db_session, sample_user):
    """Create a sample portfolio for testing."""
    portfolio = Portfolio(
        user_id=sample_user.id,
        name="Test Portfolio",
        description="A test portfolio for unit testing",
        portfolio_type=PortfolioType.INSTITUTIONAL.value,
        status=PortfolioStatus.ACTIVE.value,
        base_currency="USD",
        total_value=Decimal("10000000.00"),
        cash_balance=Decimal("1000000.00"),
        invested_amount=Decimal("9000000.00"),
        benchmark_name="Bloomberg Aggregate",
        max_position_size=Decimal("10.0"),
        min_credit_rating="BBB-",
        max_duration=Decimal("8.0")
    )
    
    db_session.add(portfolio)
    db_session.commit()
    db_session.refresh(portfolio)
    
    return portfolio


@pytest.fixture
def sample_holding(db_session, sample_portfolio, sample_bond):
    """Create a sample portfolio holding."""
    holding = PortfolioHolding(
        portfolio_id=sample_portfolio.id,
        bond_id=sample_bond.id,
        quantity=Decimal("1000000.00"),  # $1M face value
        average_cost=Decimal("101.25"),
        current_price=Decimal("102.50"),
        book_value=Decimal("1012500.00"),
        market_value=Decimal("1025000.00"),
        unrealized_pnl=Decimal("12500.00"),
        weight=Decimal("10.25"),
        is_active=True
    )
    
    db_session.add(holding)
    db_session.commit()
    db_session.refresh(holding)
    
    return holding


@pytest.fixture
def sample_trade(db_session, sample_user, sample_portfolio, sample_bond):
    """Create a sample trade for testing."""
    trade = Trade(
        user_id=sample_user.id,
        portfolio_id=sample_portfolio.id,
        bond_id=sample_bond.id,
        trade_id="TRD-20240101-TEST001",
        side=OrderSide.BUY.value,
        quantity=Decimal("500000.00"),
        price=Decimal("102.00"),
        gross_amount=Decimal("510000.00"),
        net_amount=Decimal("510500.00"),
        status=TradeStatus.EXECUTED.value,
        trade_date=datetime.utcnow(),
        settlement_date=datetime.utcnow() + timedelta(days=2),
        yield_to_maturity=Decimal("4.85"),
        notes="Test trade"
    )
    
    db_session.add(trade)
    db_session.commit()
    db_session.refresh(trade)
    
    return trade


@pytest.fixture
def auth_headers(sample_user):
    """Create authentication headers for API testing."""
    from api.auth import create_access_token
    
    token_data = {
        "sub": str(sample_user.id),
        "username": sample_user.username,
        "role": sample_user.role
    }
    
    access_token = create_access_token(token_data)
    
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def admin_auth_headers(admin_user):
    """Create admin authentication headers for API testing."""
    from api.auth import create_access_token
    
    token_data = {
        "sub": str(admin_user.id),
        "username": admin_user.username,
        "role": admin_user.role
    }
    
    access_token = create_access_token(token_data)
    
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def mock_market_data():
    """Mock market data for testing."""
    return {
        "price": 102.50,
        "yield": 4.85,
        "bid": 102.25,
        "ask": 102.75,
        "volume": 1000000,
        "timestamp": datetime.utcnow()
    }


@pytest.fixture
def temp_file():
    """Create a temporary file for testing."""
    with tempfile.NamedTemporaryFile(delete=False) as tmp:
        yield tmp.name
    
    # Cleanup
    try:
        os.unlink(tmp.name)
    except OSError:
        pass


# Test data factories
class BondFactory:
    """Factory for creating test bonds."""
    
    @staticmethod
    def create_bond(db_session, **kwargs):
        defaults = {
            "cusip": "87654321B",
            "issuer_name": "Factory Corp",
            "bond_type": BondType.CORPORATE.value,
            "face_value": Decimal("1000.00"),
            "coupon_rate": Decimal("4.50"),
            "maturity_date": date.today() + timedelta(days=365*3),
            "current_price": Decimal("100.00"),
            "yield_to_maturity": Decimal("4.50"),
            "liquidity_score": Decimal("60.0"),
            "liquidity_tier": LiquidityTier.TIER_2.value,
            "is_active": True
        }
        defaults.update(kwargs)
        
        bond = Bond(**defaults)
        db_session.add(bond)
        db_session.commit()
        db_session.refresh(bond)
        
        return bond


class UserFactory:
    """Factory for creating test users."""
    
    @staticmethod
    def create_user(db_session, **kwargs):
        defaults = {
            "email": "<EMAIL>",
            "username": "factoryuser",
            "first_name": "Factory",
            "last_name": "User",
            "role": UserRole.VIEWER.value,
            "status": UserStatus.ACTIVE.value,
            "is_active": True
        }
        defaults.update(kwargs)
        
        user = User(**defaults)
        user.set_password("FactoryPassword123!")
        
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        return user
