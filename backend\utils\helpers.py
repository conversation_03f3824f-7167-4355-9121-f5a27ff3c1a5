"""
Helper utility functions for LiquidBond application
"""

import re
import uuid
import hashlib
import secrets
from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import Optional, Any, Dict, List
import logging
from cryptography.fernet import Fernet
import os
import base64

logger = logging.getLogger(__name__)

# Encryption key (in production, store securely)
ENCRYPTION_KEY = os.getenv('ENCRYPTION_KEY', Fernet.generate_key())
if isinstance(ENCRYPTION_KEY, str):
    ENCRYPTION_KEY = ENCRYPTION_KEY.encode()

cipher_suite = Fernet(ENCRYPTION_KEY)


def format_currency(amount: Optional[Decimal], currency: str = "USD", decimal_places: int = 2) -> str:
    """Format decimal amount as currency string"""
    if amount is None:
        return "N/A"
    
    try:
        formatted_amount = f"{float(amount):,.{decimal_places}f}"
        
        currency_symbols = {
            "USD": "$",
            "EUR": "€",
            "GBP": "£",
            "JPY": "¥",
            "CAD": "C$",
            "AUD": "A$"
        }
        
        symbol = currency_symbols.get(currency, currency)
        return f"{symbol}{formatted_amount}"
        
    except (ValueError, TypeError):
        return "N/A"


def format_percentage(value: Optional[Decimal], decimal_places: int = 2) -> str:
    """Format decimal value as percentage string"""
    if value is None:
        return "N/A"
    
    try:
        return f"{float(value):.{decimal_places}f}%"
    except (ValueError, TypeError):
        return "N/A"


def calculate_days_between(start_date: date, end_date: date) -> int:
    """Calculate number of days between two dates"""
    try:
        return (end_date - start_date).days
    except (TypeError, AttributeError):
        return 0


def generate_unique_id(prefix: str = "", length: int = 8) -> str:
    """Generate unique identifier with optional prefix"""
    unique_part = str(uuid.uuid4()).replace('-', '')[:length].upper()
    return f"{prefix}{unique_part}" if prefix else unique_part


def sanitize_string(input_string: Optional[str], max_length: int = 255) -> str:
    """Sanitize string input for security"""
    if not input_string:
        return ""
    
    # Remove potentially dangerous characters
    sanitized = re.sub(r'[<>"\';\\]', '', str(input_string))
    
    # Trim whitespace and limit length
    sanitized = sanitized.strip()[:max_length]
    
    return sanitized


def validate_cusip(cusip: str) -> bool:
    """Validate CUSIP format and check digit"""
    if not cusip or len(cusip) != 9:
        return False
    
    # CUSIP should be 8 alphanumeric characters + 1 check digit
    if not re.match(r'^[0-9A-Z]{8}[0-9]$', cusip.upper()):
        return False
    
    # Validate check digit using CUSIP algorithm
    try:
        cusip = cusip.upper()
        total = 0
        
        for i, char in enumerate(cusip[:8]):
            if char.isdigit():
                value = int(char)
            else:
                value = ord(char) - ord('A') + 10
            
            if i % 2 == 1:  # Odd positions (1-indexed)
                value *= 2
            
            total += value // 10 + value % 10
        
        check_digit = (10 - (total % 10)) % 10
        return int(cusip[8]) == check_digit
        
    except (ValueError, IndexError):
        return False


def validate_isin(isin: str) -> bool:
    """Validate ISIN format and check digit"""
    if not isin or len(isin) != 12:
        return False
    
    # ISIN should be 2 letters + 9 alphanumeric + 1 check digit
    if not re.match(r'^[A-Z]{2}[0-9A-Z]{9}[0-9]$', isin.upper()):
        return False
    
    # Validate check digit using Luhn algorithm
    try:
        isin = isin.upper()
        
        # Convert letters to numbers
        numeric_string = ""
        for char in isin[:11]:
            if char.isdigit():
                numeric_string += char
            else:
                numeric_string += str(ord(char) - ord('A') + 10)
        
        # Apply Luhn algorithm
        total = 0
        reverse_digits = numeric_string[::-1]
        
        for i, digit in enumerate(reverse_digits):
            n = int(digit)
            if i % 2 == 1:  # Every second digit from right
                n *= 2
                if n > 9:
                    n = n // 10 + n % 10
            total += n
        
        check_digit = (10 - (total % 10)) % 10
        return int(isin[11]) == check_digit
        
    except (ValueError, IndexError):
        return False


def hash_string(input_string: str, salt: Optional[str] = None) -> str:
    """Hash string using SHA-256 with optional salt"""
    if salt is None:
        salt = secrets.token_hex(16)
    
    combined = f"{input_string}{salt}"
    hash_object = hashlib.sha256(combined.encode())
    return hash_object.hexdigest()


def encrypt_data(data: str) -> str:
    """Encrypt sensitive data"""
    try:
        encrypted_data = cipher_suite.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    except Exception as e:
        logger.error(f"Encryption failed: {e}")
        raise


def decrypt_data(encrypted_data: str) -> str:
    """Decrypt sensitive data"""
    try:
        encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted_data = cipher_suite.decrypt(encrypted_bytes)
        return decrypted_data.decode()
    except Exception as e:
        logger.error(f"Decryption failed: {e}")
        raise


def calculate_business_days(start_date: date, end_date: date) -> int:
    """Calculate number of business days between two dates"""
    try:
        current_date = start_date
        business_days = 0
        
        while current_date <= end_date:
            if current_date.weekday() < 5:  # Monday = 0, Friday = 4
                business_days += 1
            current_date += timedelta(days=1)
        
        return business_days
    except (TypeError, AttributeError):
        return 0


def round_to_nearest(value: Decimal, nearest: Decimal) -> Decimal:
    """Round value to nearest specified increment"""
    try:
        return round(value / nearest) * nearest
    except (TypeError, ZeroDivisionError):
        return value


def calculate_yield_to_maturity(
    current_price: Decimal,
    face_value: Decimal,
    coupon_rate: Decimal,
    years_to_maturity: Decimal
) -> Optional[Decimal]:
    """Calculate approximate yield to maturity (simplified formula)"""
    try:
        if years_to_maturity <= 0:
            return None
        
        annual_coupon = face_value * (coupon_rate / 100)
        
        # Simplified YTM approximation
        numerator = annual_coupon + (face_value - current_price) / years_to_maturity
        denominator = (face_value + current_price) / 2
        
        ytm = (numerator / denominator) * 100
        return Decimal(str(round(float(ytm), 4)))
        
    except (TypeError, ZeroDivisionError, ValueError):
        return None


def calculate_duration(
    cash_flows: List[Dict[str, Any]],
    yield_rate: Decimal
) -> Optional[Decimal]:
    """Calculate modified duration given cash flows"""
    try:
        if not cash_flows or yield_rate <= 0:
            return None
        
        present_value = Decimal('0')
        weighted_time = Decimal('0')
        
        for cf in cash_flows:
            time_period = Decimal(str(cf['time']))
            cash_flow = Decimal(str(cf['amount']))
            
            pv_factor = 1 / ((1 + yield_rate / 100) ** time_period)
            pv = cash_flow * Decimal(str(pv_factor))
            
            present_value += pv
            weighted_time += time_period * pv
        
        if present_value > 0:
            duration = weighted_time / present_value
            return round(duration, 4)
        
        return None
        
    except (TypeError, ValueError, ZeroDivisionError):
        return None


def format_bond_identifier(cusip: Optional[str], isin: Optional[str]) -> str:
    """Format bond identifier for display"""
    if cusip:
        return f"CUSIP: {cusip}"
    elif isin:
        return f"ISIN: {isin}"
    else:
        return "No Identifier"


def calculate_accrued_interest(
    face_value: Decimal,
    coupon_rate: Decimal,
    last_payment_date: date,
    settlement_date: date,
    payment_frequency: int = 2
) -> Decimal:
    """Calculate accrued interest for a bond"""
    try:
        days_since_payment = (settlement_date - last_payment_date).days
        days_in_period = 365 // payment_frequency
        
        accrual_factor = Decimal(str(days_since_payment)) / Decimal(str(days_in_period))
        period_coupon = face_value * (coupon_rate / 100) / payment_frequency
        
        return period_coupon * accrual_factor
        
    except (TypeError, ZeroDivisionError):
        return Decimal('0')


def validate_trade_settlement_date(trade_date: date, settlement_date: date) -> bool:
    """Validate that settlement date is appropriate for trade date"""
    try:
        # Settlement should be T+0 to T+5 for most bonds
        days_diff = (settlement_date - trade_date).days
        return 0 <= days_diff <= 5
    except (TypeError, AttributeError):
        return False


def calculate_price_from_yield(
    face_value: Decimal,
    coupon_rate: Decimal,
    yield_rate: Decimal,
    years_to_maturity: Decimal,
    payment_frequency: int = 2
) -> Optional[Decimal]:
    """Calculate bond price from yield (simplified)"""
    try:
        if years_to_maturity <= 0 or yield_rate < 0:
            return None
        
        periods = int(years_to_maturity * payment_frequency)
        period_yield = yield_rate / (100 * payment_frequency)
        period_coupon = face_value * (coupon_rate / 100) / payment_frequency
        
        # Present value of coupon payments
        pv_coupons = Decimal('0')
        for period in range(1, periods + 1):
            pv_coupons += period_coupon / ((1 + period_yield) ** period)
        
        # Present value of principal
        pv_principal = face_value / ((1 + period_yield) ** periods)
        
        price = pv_coupons + pv_principal
        return round(price, 2)
        
    except (TypeError, ValueError, ZeroDivisionError):
        return None
