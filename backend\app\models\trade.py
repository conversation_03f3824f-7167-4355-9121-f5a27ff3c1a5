"""
Trading and order management models
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional
from enum import Enum

from sqlalchemy import Column, Integer, String, DateTime, Numeric, Boolean, Text, ForeignKey, Index
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid

from app.database import Base


class OrderSide(str, Enum):
    """Order side enumeration"""
    BUY = "buy"
    SELL = "sell"


class OrderType(str, Enum):
    """Order type enumeration"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    IOC = "ioc"  # Immediate or Cancel
    FOK = "fok"  # Fill or Kill


class OrderStatus(str, Enum):
    """Order status enumeration"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"


class TradeStatus(str, Enum):
    """Trade status enumeration"""
    PENDING = "pending"
    EXECUTED = "executed"
    SETTLED = "settled"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Trade(Base):
    """Main trade record model"""
    __tablename__ = "trades"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    portfolio_id = Column(UUID(as_uuid=True), ForeignKey("portfolios.id"), nullable=False)
    bond_id = Column(UUID(as_uuid=True), ForeignKey("bonds.id"), nullable=False)
    
    # Trade Identification
    trade_id = Column(String(50), unique=True, index=True, nullable=False)
    external_trade_id = Column(String(100))  # External system trade ID
    
    # Trade Details
    side = Column(String(10), nullable=False, index=True)
    quantity = Column(Numeric(15, 2), nullable=False)  # Face value amount
    price = Column(Numeric(10, 4), nullable=False)     # Price per $100 face value
    
    # Financial Details
    gross_amount = Column(Numeric(15, 2), nullable=False)  # Gross trade amount
    accrued_interest = Column(Numeric(15, 2), default=0.00)
    commission = Column(Numeric(10, 2), default=0.00)
    fees = Column(Numeric(10, 2), default=0.00)
    net_amount = Column(Numeric(15, 2), nullable=False)    # Net settlement amount
    
    # Yield and Spread
    yield_to_maturity = Column(Numeric(8, 4))
    spread_to_benchmark = Column(Numeric(8, 4))
    
    # Status and Timing
    status = Column(String(20), nullable=False, default=TradeStatus.PENDING, index=True)
    trade_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    settlement_date = Column(DateTime)
    
    # Execution Details
    execution_venue = Column(String(100))  # Trading platform/venue
    counterparty = Column(String(255))     # Counterparty name
    trader_id = Column(String(100))        # Executing trader
    
    # Risk and Compliance
    pre_trade_risk_check = Column(Boolean, default=False)
    post_trade_compliance = Column(Boolean, default=False)
    risk_limit_breaches = Column(JSONB)    # Any risk limit breaches
    
    # Alternative Recommendation
    is_alternative_trade = Column(Boolean, default=False)  # If this was an AI-suggested alternative
    original_bond_id = Column(UUID(as_uuid=True), ForeignKey("bonds.id"))  # Original illiquid bond
    similarity_score = Column(Numeric(5, 4))  # Similarity score to original bond
    
    # Notes and Comments
    notes = Column(Text)
    internal_comments = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="trades")
    portfolio = relationship("Portfolio")
    bond = relationship("Bond", foreign_keys=[bond_id])
    original_bond = relationship("Bond", foreign_keys=[original_bond_id])
    orders = relationship("TradeOrder", back_populates="trade", cascade="all, delete-orphan")
    executions = relationship("TradeExecution", back_populates="trade", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_trade_user_date', 'user_id', 'trade_date'),
        Index('idx_trade_portfolio_date', 'portfolio_id', 'trade_date'),
        Index('idx_trade_bond_date', 'bond_id', 'trade_date'),
        Index('idx_trade_status_date', 'status', 'trade_date'),
    )
    
    def __repr__(self):
        return f"<Trade(trade_id='{self.trade_id}', side='{self.side}', quantity={self.quantity})>"
    
    @property
    def is_buy(self) -> bool:
        """Check if trade is a buy order"""
        return self.side == OrderSide.BUY
    
    @property
    def is_sell(self) -> bool:
        """Check if trade is a sell order"""
        return self.side == OrderSide.SELL
    
    def calculate_net_amount(self):
        """Calculate net settlement amount"""
        if self.is_buy:
            self.net_amount = self.gross_amount + self.accrued_interest + self.commission + self.fees
        else:
            self.net_amount = self.gross_amount + self.accrued_interest - self.commission - self.fees


class TradeOrder(Base):
    """Order management model"""
    __tablename__ = "trade_orders"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    trade_id = Column(UUID(as_uuid=True), ForeignKey("trades.id"), nullable=False)
    
    # Order Identification
    order_id = Column(String(50), unique=True, index=True, nullable=False)
    external_order_id = Column(String(100))  # External system order ID
    
    # Order Details
    order_type = Column(String(20), nullable=False, index=True)
    side = Column(String(10), nullable=False)
    quantity = Column(Numeric(15, 2), nullable=False)
    price = Column(Numeric(10, 4))  # Limit price (if applicable)
    stop_price = Column(Numeric(10, 4))  # Stop price (if applicable)
    
    # Order Status
    status = Column(String(20), nullable=False, default=OrderStatus.PENDING, index=True)
    filled_quantity = Column(Numeric(15, 2), default=0.00)
    remaining_quantity = Column(Numeric(15, 2))
    
    # Timing
    order_time = Column(DateTime, nullable=False, default=datetime.utcnow)
    expiry_time = Column(DateTime)
    last_update_time = Column(DateTime)
    
    # Execution Details
    average_fill_price = Column(Numeric(10, 4))
    total_commission = Column(Numeric(10, 2), default=0.00)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    trade = relationship("Trade", back_populates="orders")
    
    # Indexes
    __table_args__ = (
        Index('idx_order_trade_status', 'trade_id', 'status'),
        Index('idx_order_status_time', 'status', 'order_time'),
    )
    
    def __repr__(self):
        return f"<TradeOrder(order_id='{self.order_id}', status='{self.status}')>"
    
    @property
    def is_filled(self) -> bool:
        """Check if order is completely filled"""
        return self.status == OrderStatus.FILLED
    
    @property
    def fill_percentage(self) -> Decimal:
        """Calculate fill percentage"""
        if self.quantity > 0:
            return (self.filled_quantity / self.quantity) * 100
        return Decimal('0')


class TradeExecution(Base):
    """Individual trade execution records"""
    __tablename__ = "trade_executions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    trade_id = Column(UUID(as_uuid=True), ForeignKey("trades.id"), nullable=False)
    
    # Execution Details
    execution_id = Column(String(50), unique=True, index=True, nullable=False)
    execution_time = Column(DateTime, nullable=False, default=datetime.utcnow)
    
    # Trade Details
    quantity = Column(Numeric(15, 2), nullable=False)
    price = Column(Numeric(10, 4), nullable=False)
    gross_amount = Column(Numeric(15, 2), nullable=False)
    
    # Venue and Counterparty
    execution_venue = Column(String(100))
    counterparty = Column(String(255))
    
    # Fees and Charges
    commission = Column(Numeric(10, 2), default=0.00)
    exchange_fees = Column(Numeric(10, 2), default=0.00)
    clearing_fees = Column(Numeric(10, 2), default=0.00)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    trade = relationship("Trade", back_populates="executions")
    
    # Indexes
    __table_args__ = (
        Index('idx_execution_trade_time', 'trade_id', 'execution_time'),
    )
    
    def __repr__(self):
        return f"<TradeExecution(execution_id='{self.execution_id}', quantity={self.quantity}, price={self.price})>"
