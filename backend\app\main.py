"""
LiquidBond FastAPI Application
Main application entry point with middleware, routes, and lifecycle management
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAP<PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from loguru import logger
import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration

from app.config import settings
from app.database import database_lifespan, check_database_health
from api import auth, bonds, portfolio, trading


# Sentry configuration for error tracking
if settings.SENTRY_DSN:
    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        integrations=[FastApiIntegration(auto_enabling=True)],
        traces_sample_rate=0.1,
        environment="production" if not settings.DEBUG else "development",
    )


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    logger.info("🚀 Starting LiquidBond application...")
    
    # Initialize databases
    async with database_lifespan():
        logger.info("✅ Database connections established")
        
        # Initialize ML models
        try:
            # Import and initialize ML services
            from services.quantum_cognition import QuantumCognitionEngine
            from services.similarity_engine import SimilarityEngine
            
            # Initialize AI engines
            app.state.quantum_engine = QuantumCognitionEngine()
            app.state.similarity_engine = SimilarityEngine()
            
            await app.state.quantum_engine.initialize()
            await app.state.similarity_engine.initialize()
            
            logger.info("🧠 AI engines initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize AI engines: {e}")
            raise
        
        logger.info("🎯 LiquidBond application ready!")
        
        yield
        
        logger.info("🛑 Shutting down LiquidBond application...")


# Create FastAPI application
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="AI-Powered Bond Liquidity Optimization Platform",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan,
)


# Middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.DEBUG else ["liquidbond.ai", "*.liquidbond.ai"]
)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": "Internal server error"}
    )


# Health check endpoints
@app.get("/health")
async def health_check():
    """Application health check"""
    return {
        "status": "healthy",
        "app": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "debug": settings.DEBUG,
    }


@app.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check including database connections"""
    db_health = await check_database_health()
    
    overall_health = all(db_health.values())
    
    return {
        "status": "healthy" if overall_health else "unhealthy",
        "app": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "databases": db_health,
        "ai_engines": {
            "quantum_cognition": hasattr(app.state, 'quantum_engine'),
            "similarity_engine": hasattr(app.state, 'similarity_engine'),
        }
    }


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Welcome to LiquidBond API",
        "description": "AI-Powered Bond Liquidity Optimization Platform",
        "version": settings.APP_VERSION,
        "docs": "/docs" if settings.DEBUG else "Contact admin for API documentation",
        "github": "https://github.com/HectorTa1989/LiquidBond_Bond_Liquidity_Optimization_App"
    }


# API version endpoint
@app.get("/api/v1/info")
async def api_info():
    """API version and feature information"""
    return {
        "api_version": "v1",
        "features": [
            "Quantum Cognition AI",
            "Bond Similarity Matching",
            "Real-time Liquidity Scoring",
            "Portfolio Optimization",
            "Risk Management",
            "Trading Integration"
        ],
        "endpoints": {
            "authentication": "/api/v1/auth",
            "bonds": "/api/v1/bonds",
            "portfolio": "/api/v1/portfolio",
            "trading": "/api/v1/trading"
        }
    }


# Include API routers
app.include_router(
    auth.router,
    prefix="/api/v1/auth",
    tags=["Authentication"]
)

app.include_router(
    bonds.router,
    prefix="/api/v1/bonds",
    tags=["Bond Analysis"]
)

app.include_router(
    portfolio.router,
    prefix="/api/v1/portfolio",
    tags=["Portfolio Management"]
)

app.include_router(
    trading.router,
    prefix="/api/v1/trading",
    tags=["Trading"]
)


# WebSocket endpoint for real-time updates
@app.websocket("/ws")
async def websocket_endpoint(websocket):
    """WebSocket endpoint for real-time market data and updates"""
    await websocket.accept()
    
    try:
        while True:
            # Send real-time market data
            data = await websocket.receive_text()
            
            # Process incoming data and send updates
            response = {
                "type": "market_update",
                "timestamp": asyncio.get_event_loop().time(),
                "data": "Real-time market data would go here"
            }
            
            await websocket.send_json(response)
            
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        await websocket.close()


# Development server
if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=settings.DEBUG,
    )
