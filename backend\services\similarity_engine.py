"""
Bond Similarity Engine
Advanced similarity matching for finding liquid alternatives to illiquid bonds
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import asyncio
import logging

from sklearn.metrics.pairwise import cosine_similarity, euclidean_distances
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
import torch
import torch.nn as nn
import torch.nn.functional as F

from .quantum_cognition import QuantumCognitionEngine

logger = logging.getLogger(__name__)


@dataclass
class SimilarityResult:
    """Result of bond similarity calculation"""
    target_bond_id: str
    similar_bond_id: str
    similarity_score: float
    feature_similarities: Dict[str, float]
    liquidity_improvement: float
    risk_adjustment: float
    confidence_score: float


class SiameseNetwork(nn.Module):
    """Siamese neural network for learning bond similarity"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 128):
        super().__init__()
        
        # Shared feature extractor
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, 64)
        )
        
        # Similarity head
        self.similarity_head = nn.Sequential(
            nn.Linear(128, 64),  # 64 * 2 for concatenated features
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
    def forward_one(self, x):
        """Forward pass for one bond"""
        return self.feature_extractor(x)
    
    def forward(self, x1, x2):
        """Forward pass for bond pair"""
        feat1 = self.forward_one(x1)
        feat2 = self.forward_one(x2)
        
        # Concatenate features
        combined = torch.cat([feat1, feat2], dim=1)
        similarity = self.similarity_head(combined)
        
        return similarity


class SimilarityEngine:
    """Main similarity engine for bond matching"""
    
    def __init__(self, feature_dim: int = 50):
        self.feature_dim = feature_dim
        
        # Neural network for similarity learning
        self.siamese_net = SiameseNetwork(feature_dim)
        self.optimizer = torch.optim.Adam(self.siamese_net.parameters(), lr=0.001)
        
        # Feature processing
        self.scaler = StandardScaler()
        self.min_max_scaler = MinMaxScaler()
        
        # Clustering for fast similarity search
        self.kmeans = KMeans(n_clusters=20, random_state=42)
        self.bond_clusters = {}
        
        # Feature weights for different similarity aspects
        self.feature_weights = {
            'financial': 0.3,      # Yield, duration, price
            'credit': 0.25,        # Rating, spread, default risk
            'liquidity': 0.2,      # Liquidity score, volume
            'structural': 0.15,    # Maturity, coupon, callable
            'market': 0.1          # Sector, issuer, market cap
        }
        
        # Quantum cognition integration
        self.quantum_engine: Optional[QuantumCognitionEngine] = None
        
        self.is_initialized = False
    
    async def initialize(self):
        """Initialize the similarity engine"""
        try:
            logger.info("Initializing Bond Similarity Engine...")
            
            # Initialize quantum cognition engine
            self.quantum_engine = QuantumCognitionEngine()
            await self.quantum_engine.initialize()
            
            # Load pre-trained similarity model
            try:
                checkpoint = torch.load("models/similarity_model.pth", map_location='cpu')
                self.siamese_net.load_state_dict(checkpoint['model_state_dict'])
                self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                logger.info("Loaded pre-trained similarity model")
            except FileNotFoundError:
                logger.info("No pre-trained similarity model found, using random initialization")
            
            self.is_initialized = True
            logger.info("✅ Bond Similarity Engine initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Bond Similarity Engine: {e}")
            raise
    
    def extract_similarity_features(self, bond_data: Dict[str, Any]) -> np.ndarray:
        """Extract features optimized for similarity calculation"""
        features = []
        
        # Financial features (normalized)
        financial_features = [
            bond_data.get('yield_to_maturity', 0) / 100,
            bond_data.get('duration', 0) / 20,  # Normalize to 20 years max
            bond_data.get('convexity', 0) / 100,
            bond_data.get('current_price', 100) / 200,  # Normalize around par
            bond_data.get('coupon_rate', 0) / 20,  # Max 20% coupon
        ]
        features.extend(financial_features)
        
        # Credit features
        credit_features = [
            self._rating_to_numeric(bond_data.get('credit_rating', 'BBB')),
            bond_data.get('credit_spread', 0) / 1000,  # Basis points to decimal
            bond_data.get('option_adjusted_spread', 0) / 1000,
        ]
        features.extend(credit_features)
        
        # Liquidity features
        liquidity_features = [
            bond_data.get('liquidity_score', 50) / 100,
            bond_data.get('bid_ask_spread', 0) / 100,
            np.log1p(bond_data.get('avg_daily_volume', 0)) / 20,  # Log-normalized volume
        ]
        features.extend(liquidity_features)
        
        # Structural features
        time_to_maturity = bond_data.get('time_to_maturity', 365) / 365
        structural_features = [
            min(time_to_maturity / 30, 1.0),  # Normalize to 30 years
            float(bond_data.get('is_callable', False)),
            float(bond_data.get('is_puttable', False)),
        ]
        features.extend(structural_features)
        
        # Market features
        market_cap = bond_data.get('market_cap', 0)
        market_features = [
            np.log1p(market_cap) / 30 if market_cap > 0 else 0,  # Log-normalized market cap
        ]
        features.extend(market_features)
        
        # Categorical features (one-hot encoded)
        # Bond type
        bond_type = bond_data.get('bond_type', 'corporate')
        type_features = self._encode_categorical(bond_type, [
            'corporate', 'government', 'municipal', 'treasury', 'agency'
        ])
        features.extend(type_features)
        
        # Sector
        sector = bond_data.get('sector', 'other')
        sector_features = self._encode_categorical(sector, [
            'financial', 'technology', 'healthcare', 'energy', 'utilities',
            'industrials', 'consumer', 'telecom', 'materials', 'other'
        ])
        features.extend(sector_features)
        
        # Country
        country = bond_data.get('country', 'US')
        country_features = self._encode_categorical(country, [
            'US', 'CA', 'GB', 'DE', 'JP', 'OTHER'
        ])
        features.extend(country_features)
        
        # Pad or truncate to feature_dim
        features = features[:self.feature_dim]
        while len(features) < self.feature_dim:
            features.append(0.0)
        
        return np.array(features, dtype=np.float32)
    
    def _rating_to_numeric(self, rating: str) -> float:
        """Convert credit rating to numeric score (0-1)"""
        rating_scores = {
            'AAA': 1.0, 'AA+': 0.95, 'AA': 0.9, 'AA-': 0.85,
            'A+': 0.8, 'A': 0.75, 'A-': 0.7,
            'BBB+': 0.65, 'BBB': 0.6, 'BBB-': 0.55,
            'BB+': 0.5, 'BB': 0.45, 'BB-': 0.4,
            'B+': 0.35, 'B': 0.3, 'B-': 0.25,
            'CCC': 0.2, 'CC': 0.15, 'C': 0.1, 'D': 0.05
        }
        return rating_scores.get(rating.upper(), 0.6)
    
    def _encode_categorical(self, value: str, categories: List[str]) -> List[float]:
        """One-hot encode categorical variable"""
        encoding = [0.0] * len(categories)
        try:
            index = categories.index(value.lower())
            encoding[index] = 1.0
        except ValueError:
            # Default to last category (usually 'other')
            encoding[-1] = 1.0
        return encoding
    
    async def calculate_similarity(self, bond1_data: Dict[str, Any], bond2_data: Dict[str, Any]) -> SimilarityResult:
        """Calculate comprehensive similarity between two bonds"""
        if not self.is_initialized:
            await self.initialize()
        
        # Extract features
        features1 = self.extract_similarity_features(bond1_data)
        features2 = self.extract_similarity_features(bond2_data)
        
        # Neural network similarity
        with torch.no_grad():
            feat1_tensor = torch.tensor(features1).unsqueeze(0)
            feat2_tensor = torch.tensor(features2).unsqueeze(0)
            nn_similarity = self.siamese_net(feat1_tensor, feat2_tensor).item()
        
        # Traditional similarity metrics
        cosine_sim = cosine_similarity([features1], [features2])[0][0]
        euclidean_sim = 1 / (1 + euclidean_distances([features1], [features2])[0][0])
        
        # Quantum similarity (if available)
        quantum_sim = 0.0
        if self.quantum_engine:
            try:
                # Create quantum states if not exist
                if bond1_data['id'] not in self.quantum_engine.bond_states:
                    await self.quantum_engine.create_quantum_state(bond1_data)
                if bond2_data['id'] not in self.quantum_engine.bond_states:
                    await self.quantum_engine.create_quantum_state(bond2_data)
                
                quantum_sim = await self.quantum_engine.calculate_quantum_similarity(
                    bond1_data['id'], bond2_data['id']
                )
            except Exception as e:
                logger.warning(f"Quantum similarity calculation failed: {e}")
        
        # Weighted combination of similarities
        overall_similarity = (
            0.4 * nn_similarity +
            0.3 * cosine_sim +
            0.2 * euclidean_sim +
            0.1 * quantum_sim
        )
        
        # Calculate feature-specific similarities
        feature_similarities = self._calculate_feature_similarities(features1, features2)
        
        # Calculate liquidity improvement
        liquidity1 = bond1_data.get('liquidity_score', 50)
        liquidity2 = bond2_data.get('liquidity_score', 50)
        liquidity_improvement = max(0, liquidity2 - liquidity1) / 100
        
        # Risk adjustment based on credit rating difference
        rating1 = self._rating_to_numeric(bond1_data.get('credit_rating', 'BBB'))
        rating2 = self._rating_to_numeric(bond2_data.get('credit_rating', 'BBB'))
        risk_adjustment = 1 - abs(rating1 - rating2)
        
        # Confidence score based on data quality and similarity consistency
        confidence_score = self._calculate_confidence(
            [nn_similarity, cosine_sim, euclidean_sim, quantum_sim],
            bond1_data, bond2_data
        )
        
        return SimilarityResult(
            target_bond_id=bond1_data['id'],
            similar_bond_id=bond2_data['id'],
            similarity_score=overall_similarity,
            feature_similarities=feature_similarities,
            liquidity_improvement=liquidity_improvement,
            risk_adjustment=risk_adjustment,
            confidence_score=confidence_score
        )
    
    def _calculate_feature_similarities(self, features1: np.ndarray, features2: np.ndarray) -> Dict[str, float]:
        """Calculate similarity for different feature groups"""
        similarities = {}
        
        # Define feature ranges for different categories
        ranges = {
            'financial': (0, 5),
            'credit': (5, 8),
            'liquidity': (8, 11),
            'structural': (11, 14),
            'market': (14, 15)
        }
        
        for category, (start, end) in ranges.items():
            if end <= len(features1):
                cat_features1 = features1[start:end]
                cat_features2 = features2[start:end]
                
                # Calculate cosine similarity for this category
                if np.linalg.norm(cat_features1) > 0 and np.linalg.norm(cat_features2) > 0:
                    similarity = cosine_similarity([cat_features1], [cat_features2])[0][0]
                else:
                    similarity = 0.0
                
                similarities[category] = float(similarity)
        
        return similarities
    
    def _calculate_confidence(self, similarities: List[float], bond1: Dict, bond2: Dict) -> float:
        """Calculate confidence score for similarity result"""
        # Consistency of different similarity measures
        sim_std = np.std(similarities)
        consistency_score = max(0, 1 - sim_std)
        
        # Data completeness score
        required_fields = ['yield_to_maturity', 'duration', 'credit_rating', 'liquidity_score']
        completeness1 = sum(1 for field in required_fields if bond1.get(field) is not None) / len(required_fields)
        completeness2 = sum(1 for field in required_fields if bond2.get(field) is not None) / len(required_fields)
        data_quality = (completeness1 + completeness2) / 2
        
        # Overall confidence
        confidence = 0.6 * consistency_score + 0.4 * data_quality
        return float(confidence)
    
    async def find_similar_bonds(self, target_bond: Dict[str, Any], candidate_bonds: List[Dict[str, Any]], 
                                top_k: int = 10, min_liquidity_improvement: float = 0.1) -> List[SimilarityResult]:
        """Find most similar bonds to target bond"""
        if not self.is_initialized:
            await self.initialize()
        
        results = []
        
        for candidate in candidate_bonds:
            # Skip if same bond
            if candidate['id'] == target_bond['id']:
                continue
            
            # Skip if liquidity improvement is insufficient
            target_liquidity = target_bond.get('liquidity_score', 50)
            candidate_liquidity = candidate.get('liquidity_score', 50)
            
            if (candidate_liquidity - target_liquidity) / 100 < min_liquidity_improvement:
                continue
            
            # Calculate similarity
            similarity_result = await self.calculate_similarity(target_bond, candidate)
            results.append(similarity_result)
        
        # Sort by similarity score (descending)
        results.sort(key=lambda x: x.similarity_score, reverse=True)
        
        return results[:top_k]
    
    def save_model(self, path: str):
        """Save similarity model"""
        torch.save({
            'model_state_dict': self.siamese_net.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'feature_dim': self.feature_dim,
        }, path)
