"""
Bond analysis and management API endpoints
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, date
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import J<PERSON>NResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from pydantic import BaseModel, Field

from app.database import get_db
from app.models.bond import Bond, BondPrice, BondRating, LiquidityTier
from app.models.user import User
from api.auth import get_current_user
from services.quantum_cognition import QuantumCognitionEngine
from services.similarity_engine import SimilarityEngine

router = APIRouter()


# Pydantic models for API
class BondResponse(BaseModel):
    """Bond response model"""
    id: str
    cusip: str
    isin: Optional[str]
    symbol: Optional[str]
    issuer_name: str
    bond_type: str
    description: Optional[str]
    face_value: Decimal
    coupon_rate: Optional[Decimal]
    maturity_date: date
    current_price: Optional[Decimal]
    yield_to_maturity: Optional[Decimal]
    duration: Optional[Decimal]
    liquidity_score: Optional[Decimal]
    liquidity_tier: Optional[str]
    sector: Optional[str]
    credit_rating: Optional[str]
    is_active: bool
    
    class Config:
        from_attributes = True


class BondSearchRequest(BaseModel):
    """Bond search request model"""
    query: Optional[str] = None
    bond_type: Optional[str] = None
    sector: Optional[str] = None
    min_yield: Optional[float] = None
    max_yield: Optional[float] = None
    min_duration: Optional[float] = None
    max_duration: Optional[float] = None
    min_liquidity_score: Optional[float] = None
    credit_ratings: Optional[List[str]] = None
    maturity_range: Optional[tuple[date, date]] = None
    limit: int = Field(default=50, le=1000)
    offset: int = Field(default=0, ge=0)


class SimilarBondsRequest(BaseModel):
    """Similar bonds request model"""
    target_cusip: str
    min_liquidity_improvement: float = Field(default=0.1, ge=0.0, le=1.0)
    max_results: int = Field(default=10, le=50)
    include_quantum_analysis: bool = True


class SimilarBondResult(BaseModel):
    """Similar bond result model"""
    bond: BondResponse
    similarity_score: float
    feature_similarities: Dict[str, float]
    liquidity_improvement: float
    risk_adjustment: float
    confidence_score: float


class LiquidityAnalysisResponse(BaseModel):
    """Liquidity analysis response model"""
    bond_id: str
    current_liquidity_score: float
    liquidity_tier: str
    factors: Dict[str, float]
    recommendations: List[str]
    market_conditions_impact: float


@router.get("/search", response_model=List[BondResponse])
async def search_bonds(
    request: BondSearchRequest = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Search bonds with various filters"""
    
    query = select(Bond).where(Bond.is_active == True)
    
    # Apply filters
    if request.query:
        query = query.where(
            or_(
                Bond.issuer_name.ilike(f"%{request.query}%"),
                Bond.cusip.ilike(f"%{request.query}%"),
                Bond.description.ilike(f"%{request.query}%")
            )
        )
    
    if request.bond_type:
        query = query.where(Bond.bond_type == request.bond_type)
    
    if request.sector:
        query = query.where(Bond.sector == request.sector)
    
    if request.min_yield:
        query = query.where(Bond.yield_to_maturity >= request.min_yield)
    
    if request.max_yield:
        query = query.where(Bond.yield_to_maturity <= request.max_yield)
    
    if request.min_duration:
        query = query.where(Bond.duration >= request.min_duration)
    
    if request.max_duration:
        query = query.where(Bond.duration <= request.max_duration)
    
    if request.min_liquidity_score:
        query = query.where(Bond.liquidity_score >= request.min_liquidity_score)
    
    if request.maturity_range:
        start_date, end_date = request.maturity_range
        query = query.where(
            and_(
                Bond.maturity_date >= start_date,
                Bond.maturity_date <= end_date
            )
        )
    
    # Apply pagination
    query = query.offset(request.offset).limit(request.limit)
    
    # Execute query
    result = await db.execute(query)
    bonds = result.scalars().all()
    
    return [BondResponse.from_orm(bond) for bond in bonds]


@router.get("/{cusip}", response_model=BondResponse)
async def get_bond_by_cusip(
    cusip: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get bond details by CUSIP"""
    
    query = select(Bond).where(
        and_(Bond.cusip == cusip, Bond.is_active == True)
    )
    
    result = await db.execute(query)
    bond = result.scalar_one_or_none()
    
    if not bond:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Bond with CUSIP {cusip} not found"
        )
    
    return BondResponse.from_orm(bond)


@router.post("/similar", response_model=List[SimilarBondResult])
async def find_similar_bonds(
    request: SimilarBondsRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Find similar bonds to target bond using AI similarity engine"""
    
    # Get target bond
    target_query = select(Bond).where(
        and_(Bond.cusip == request.target_cusip, Bond.is_active == True)
    )
    target_result = await db.execute(target_query)
    target_bond = target_result.scalar_one_or_none()
    
    if not target_bond:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Target bond with CUSIP {request.target_cusip} not found"
        )
    
    # Get candidate bonds (more liquid than target)
    candidates_query = select(Bond).where(
        and_(
            Bond.is_active == True,
            Bond.id != target_bond.id,
            Bond.liquidity_score > target_bond.liquidity_score + (request.min_liquidity_improvement * 100)
        )
    ).limit(500)  # Limit candidates for performance
    
    candidates_result = await db.execute(candidates_query)
    candidate_bonds = candidates_result.scalars().all()
    
    if not candidate_bonds:
        return []
    
    # Initialize similarity engine
    similarity_engine = SimilarityEngine()
    await similarity_engine.initialize()
    
    # Convert to dict format for similarity engine
    target_data = _bond_to_dict(target_bond)
    candidate_data = [_bond_to_dict(bond) for bond in candidate_bonds]
    
    # Find similar bonds
    similarity_results = await similarity_engine.find_similar_bonds(
        target_bond=target_data,
        candidate_bonds=candidate_data,
        top_k=request.max_results,
        min_liquidity_improvement=request.min_liquidity_improvement
    )
    
    # Convert results to response format
    response_results = []
    for result in similarity_results:
        # Find the bond object
        similar_bond = next(
            bond for bond in candidate_bonds 
            if str(bond.id) == result.similar_bond_id
        )
        
        response_results.append(SimilarBondResult(
            bond=BondResponse.from_orm(similar_bond),
            similarity_score=result.similarity_score,
            feature_similarities=result.feature_similarities,
            liquidity_improvement=result.liquidity_improvement,
            risk_adjustment=result.risk_adjustment,
            confidence_score=result.confidence_score
        ))
    
    return response_results


@router.get("/{cusip}/liquidity-analysis", response_model=LiquidityAnalysisResponse)
async def analyze_bond_liquidity(
    cusip: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Analyze bond liquidity and provide recommendations"""
    
    # Get bond
    query = select(Bond).where(
        and_(Bond.cusip == cusip, Bond.is_active == True)
    )
    result = await db.execute(query)
    bond = result.scalar_one_or_none()
    
    if not bond:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Bond with CUSIP {cusip} not found"
        )
    
    # Analyze liquidity factors
    factors = {
        "bid_ask_spread": float(bond.bid_ask_spread or 0),
        "daily_volume": float(bond.avg_daily_volume or 0),
        "time_to_maturity": bond.time_to_maturity or 0,
        "credit_quality": _get_credit_score(bond),
        "issuer_size": _get_issuer_size_score(bond),
        "sector_liquidity": _get_sector_liquidity_score(bond.sector)
    }
    
    # Generate recommendations
    recommendations = _generate_liquidity_recommendations(bond, factors)
    
    # Market conditions impact (simplified)
    market_conditions_impact = _calculate_market_impact(bond)
    
    return LiquidityAnalysisResponse(
        bond_id=str(bond.id),
        current_liquidity_score=float(bond.liquidity_score or 0),
        liquidity_tier=bond.liquidity_tier or "tier_3",
        factors=factors,
        recommendations=recommendations,
        market_conditions_impact=market_conditions_impact
    )


@router.get("/{cusip}/price-history")
async def get_bond_price_history(
    cusip: str,
    days: int = Query(default=30, ge=1, le=365),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get bond price history"""
    
    # Get bond
    bond_query = select(Bond).where(Bond.cusip == cusip)
    bond_result = await db.execute(bond_query)
    bond = bond_result.scalar_one_or_none()
    
    if not bond:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Bond with CUSIP {cusip} not found"
        )
    
    # Get price history
    start_date = datetime.utcnow().date() - timedelta(days=days)
    
    price_query = select(BondPrice).where(
        and_(
            BondPrice.bond_id == bond.id,
            BondPrice.price_date >= start_date
        )
    ).order_by(BondPrice.price_date.desc())
    
    price_result = await db.execute(price_query)
    prices = price_result.scalars().all()
    
    return {
        "bond_cusip": cusip,
        "period_days": days,
        "prices": [
            {
                "date": price.price_date.isoformat(),
                "price": float(price.price),
                "yield": float(price.yield_value) if price.yield_value else None,
                "volume": float(price.volume) if price.volume else None,
                "bid_ask_spread": float(price.bid_ask_spread) if price.bid_ask_spread else None
            }
            for price in prices
        ]
    }


# Helper functions
def _bond_to_dict(bond: Bond) -> Dict[str, Any]:
    """Convert Bond model to dictionary for similarity engine"""
    return {
        'id': str(bond.id),
        'cusip': bond.cusip,
        'issuer_name': bond.issuer_name,
        'bond_type': bond.bond_type,
        'coupon_rate': float(bond.coupon_rate or 0),
        'yield_to_maturity': float(bond.yield_to_maturity or 0),
        'duration': float(bond.duration or 0),
        'convexity': float(bond.convexity or 0),
        'current_price': float(bond.current_price or 100),
        'liquidity_score': float(bond.liquidity_score or 50),
        'credit_spread': float(bond.credit_spread or 0),
        'bid_ask_spread': float(bond.bid_ask_spread or 0),
        'avg_daily_volume': float(bond.avg_daily_volume or 0),
        'time_to_maturity': bond.time_to_maturity,
        'sector': bond.sector,
        'country': bond.country,
        'is_callable': bond.is_callable,
        'is_puttable': bond.is_puttable,
        'credit_rating': _get_latest_rating(bond),
        'market_cap': 1000000000  # Placeholder - would come from issuer data
    }


def _get_latest_rating(bond: Bond) -> str:
    """Get latest credit rating for bond"""
    if bond.ratings:
        latest_rating = max(bond.ratings, key=lambda r: r.rating_date)
        return latest_rating.rating
    return "BBB"


def _get_credit_score(bond: Bond) -> float:
    """Get credit quality score (0-1)"""
    rating = _get_latest_rating(bond)
    rating_scores = {
        'AAA': 1.0, 'AA+': 0.95, 'AA': 0.9, 'AA-': 0.85,
        'A+': 0.8, 'A': 0.75, 'A-': 0.7,
        'BBB+': 0.65, 'BBB': 0.6, 'BBB-': 0.55,
        'BB+': 0.5, 'BB': 0.45, 'BB-': 0.4,
        'B+': 0.35, 'B': 0.3, 'B-': 0.25,
        'CCC': 0.2, 'CC': 0.15, 'C': 0.1, 'D': 0.05
    }
    return rating_scores.get(rating, 0.6)


def _get_issuer_size_score(bond: Bond) -> float:
    """Get issuer size score (placeholder)"""
    # This would typically come from issuer financial data
    return 0.7


def _get_sector_liquidity_score(sector: Optional[str]) -> float:
    """Get sector liquidity score"""
    sector_scores = {
        'financial': 0.8,
        'technology': 0.7,
        'utilities': 0.6,
        'healthcare': 0.65,
        'energy': 0.5,
        'industrials': 0.6,
        'consumer': 0.7,
        'telecom': 0.55,
        'materials': 0.5
    }
    return sector_scores.get(sector or 'other', 0.5)


def _generate_liquidity_recommendations(bond: Bond, factors: Dict[str, float]) -> List[str]:
    """Generate liquidity improvement recommendations"""
    recommendations = []
    
    if factors['bid_ask_spread'] > 0.5:
        recommendations.append("Consider trading during market hours for better spreads")
    
    if factors['daily_volume'] < 1000000:
        recommendations.append("Low trading volume - consider block trading networks")
    
    if factors['time_to_maturity'] > 10 * 365:
        recommendations.append("Long maturity may impact liquidity - consider shorter alternatives")
    
    if factors['credit_quality'] < 0.6:
        recommendations.append("Credit quality concerns may affect liquidity")
    
    return recommendations


def _calculate_market_impact(bond: Bond) -> float:
    """Calculate market conditions impact on liquidity"""
    # Simplified calculation - would use real market data
    base_impact = 0.0
    
    # Sector-specific impacts
    if bond.sector == 'energy':
        base_impact -= 0.1  # Energy sector volatility
    elif bond.sector == 'technology':
        base_impact += 0.05  # Tech sector premium
    
    return base_impact
