"""
Portfolio management models
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional, List
from enum import Enum

from sqlalchemy import Column, Integer, String, DateTime, Date, Numeric, Boolean, Text, ForeignKey, Index
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid

from app.database import Base


class PortfolioType(str, Enum):
    """Portfolio type enumeration"""
    INSTITUTIONAL = "institutional"
    PENSION = "pension"
    INSURANCE = "insurance"
    MUTUAL_FUND = "mutual_fund"
    HEDGE_FUND = "hedge_fund"
    SOVEREIGN = "sovereign"
    CORPORATE = "corporate"
    PERSONAL = "personal"


class PortfolioStatus(str, Enum):
    """Portfolio status enumeration"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    CLOSED = "closed"
    SUSPENDED = "suspended"


class Portfolio(Base):
    """Portfolio model for managing bond holdings"""
    __tablename__ = "portfolios"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Basic Information
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    portfolio_type = Column(String(50), nullable=False, index=True)
    status = Column(String(20), nullable=False, default=PortfolioStatus.ACTIVE, index=True)
    
    # Financial Information
    base_currency = Column(String(3), default="USD", nullable=False)
    total_value = Column(Numeric(20, 2), default=0.00)
    cash_balance = Column(Numeric(20, 2), default=0.00)
    invested_amount = Column(Numeric(20, 2), default=0.00)
    
    # Performance Metrics
    total_return = Column(Numeric(10, 4))  # Total return percentage
    ytd_return = Column(Numeric(10, 4))    # Year-to-date return
    duration = Column(Numeric(8, 4))       # Portfolio duration
    yield_to_maturity = Column(Numeric(8, 4))  # Portfolio YTM
    
    # Risk Metrics
    var_95 = Column(Numeric(15, 2))        # Value at Risk (95%)
    expected_shortfall = Column(Numeric(15, 2))  # Expected Shortfall
    tracking_error = Column(Numeric(8, 4))  # Tracking error vs benchmark
    information_ratio = Column(Numeric(8, 4))  # Information ratio
    
    # Liquidity Metrics
    liquidity_score = Column(Numeric(5, 2))  # Overall portfolio liquidity (0-100)
    liquid_percentage = Column(Numeric(5, 2))  # Percentage of liquid holdings
    illiquid_percentage = Column(Numeric(5, 2))  # Percentage of illiquid holdings
    
    # Concentration Metrics
    top_10_concentration = Column(Numeric(5, 2))  # Top 10 holdings concentration
    sector_concentration = Column(JSONB)  # Sector concentration breakdown
    rating_distribution = Column(JSONB)   # Credit rating distribution
    
    # Benchmark Information
    benchmark_name = Column(String(100))
    benchmark_return = Column(Numeric(10, 4))
    
    # Constraints and Limits
    max_position_size = Column(Numeric(5, 2), default=10.00)  # Max % per position
    min_credit_rating = Column(String(10))  # Minimum credit rating
    max_duration = Column(Numeric(8, 4))    # Maximum duration limit
    
    # Compliance
    investment_mandate = Column(JSONB)  # Investment guidelines and restrictions
    compliance_status = Column(String(20), default="compliant")
    last_compliance_check = Column(DateTime)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_rebalanced = Column(DateTime)
    
    # Relationships
    user = relationship("User", back_populates="portfolios")
    holdings = relationship("PortfolioHolding", back_populates="portfolio", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_portfolio_user_status', 'user_id', 'status'),
        Index('idx_portfolio_type_status', 'portfolio_type', 'status'),
    )
    
    def __repr__(self):
        return f"<Portfolio(name='{self.name}', user_id='{self.user_id}')>"
    
    @property
    def number_of_holdings(self) -> int:
        """Get number of holdings in portfolio"""
        return len(self.holdings)
    
    @property
    def is_compliant(self) -> bool:
        """Check if portfolio is compliant"""
        return self.compliance_status == "compliant"
    
    def calculate_total_value(self) -> Decimal:
        """Calculate total portfolio value from holdings"""
        total = sum(holding.market_value or 0 for holding in self.holdings)
        return total + (self.cash_balance or 0)


class PortfolioHolding(Base):
    """Individual bond holdings within a portfolio"""
    __tablename__ = "portfolio_holdings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    portfolio_id = Column(UUID(as_uuid=True), ForeignKey("portfolios.id"), nullable=False)
    bond_id = Column(UUID(as_uuid=True), ForeignKey("bonds.id"), nullable=False)
    
    # Position Information
    quantity = Column(Numeric(15, 2), nullable=False)  # Face value amount
    average_cost = Column(Numeric(10, 4), nullable=False)  # Average cost per $100 face
    current_price = Column(Numeric(10, 4))  # Current market price per $100 face
    
    # Calculated Values
    book_value = Column(Numeric(15, 2))    # Book value of position
    market_value = Column(Numeric(15, 2))  # Current market value
    unrealized_pnl = Column(Numeric(15, 2))  # Unrealized P&L
    
    # Position Metrics
    weight = Column(Numeric(5, 4))         # Position weight in portfolio
    duration_contribution = Column(Numeric(8, 4))  # Contribution to portfolio duration
    yield_contribution = Column(Numeric(8, 4))     # Contribution to portfolio yield
    
    # Risk Metrics
    var_contribution = Column(Numeric(15, 2))  # VaR contribution
    beta = Column(Numeric(8, 4))               # Beta vs portfolio benchmark
    
    # Trading Information
    last_trade_date = Column(Date)
    last_trade_price = Column(Numeric(10, 4))
    
    # Status
    is_active = Column(Boolean, default=True, index=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    portfolio = relationship("Portfolio", back_populates="holdings")
    bond = relationship("Bond")
    
    # Indexes
    __table_args__ = (
        Index('idx_holding_portfolio_bond', 'portfolio_id', 'bond_id'),
        Index('idx_holding_portfolio_active', 'portfolio_id', 'is_active'),
    )
    
    def __repr__(self):
        return f"<PortfolioHolding(portfolio_id='{self.portfolio_id}', bond_id='{self.bond_id}', quantity={self.quantity})>"
    
    def update_market_value(self, current_price: Decimal):
        """Update market value based on current price"""
        self.current_price = current_price
        self.market_value = (self.quantity * current_price) / 100  # Price is per $100 face
        self.unrealized_pnl = self.market_value - self.book_value
        self.updated_at = datetime.utcnow()
    
    def calculate_book_value(self):
        """Calculate book value of position"""
        self.book_value = (self.quantity * self.average_cost) / 100
    
    @property
    def total_return_pct(self) -> Optional[Decimal]:
        """Calculate total return percentage"""
        if self.book_value and self.book_value > 0:
            return ((self.market_value - self.book_value) / self.book_value) * 100
        return None
