{"name": "liquidbond-frontend", "version": "1.0.0", "description": "LiquidBond - AI-Powered Bond Liquidity Optimization Platform Frontend", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "axios": "^1.6.2", "recharts": "^2.8.0", "d3": "^7.8.5", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "@ant-design/plots": "^2.0.2", "styled-components": "^6.1.6", "framer-motion": "^10.16.16", "react-hook-form": "^7.48.2", "react-table": "^7.8.0", "react-virtual": "^2.10.4", "date-fns": "^2.30.0", "lodash": "^4.17.21", "numeral": "^2.0.6", "react-helmet-async": "^2.0.4", "react-hot-toast": "^2.4.1", "zustand": "^4.4.7", "immer": "^10.0.3", "react-use": "^17.4.2", "react-intersection-observer": "^9.5.3", "react-window": "^1.8.8", "react-virtualized-auto-sizer": "^1.0.20", "react-error-boundary": "^4.0.11", "web-vitals": "^3.5.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jsdom": "^23.0.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}