"""
Tests for database models
"""

import pytest
from datetime import datetime, date, timedelta
from decimal import Decimal

from app.models.user import User, UserProfile, UserRole, UserStatus
from app.models.bond import Bond, BondPrice, BondRating, BondType, LiquidityTier
from app.models.portfolio import Portfolio, PortfolioHolding, PortfolioType, PortfolioStatus
from app.models.trade import Trade, TradeOrder, TradeExecution, OrderSide, OrderType, OrderStatus, TradeStatus


class TestUserModel:
    """Test User model functionality."""
    
    def test_create_user(self, db_session):
        """Test creating a new user."""
        user = User(
            email="<EMAIL>",
            username="testuser",
            first_name="Test",
            last_name="User",
            role=UserRole.TRADER.value
        )
        user.set_password("TestPassword123!")
        
        db_session.add(user)
        db_session.commit()
        
        assert user.id is not None
        assert user.email == "<EMAIL>"
        assert user.username == "testuser"
        assert user.full_name == "Test User"
        assert user.role == UserRole.TRADER.value
        assert user.status == UserStatus.PENDING.value  # Default
        assert user.is_active is True  # Default
        assert user.verify_password("TestPassword123!")
        assert not user.verify_password("WrongPassword")
    
    def test_user_permissions(self, db_session):
        """Test user permission properties."""
        # Test admin user
        admin = User(
            email="<EMAIL>",
            username="admin",
            role=UserRole.ADMIN.value
        )
        assert admin.is_admin
        assert admin.can_trade
        assert admin.can_manage_portfolio
        
        # Test portfolio manager
        pm = User(
            email="<EMAIL>",
            username="pm",
            role=UserRole.PORTFOLIO_MANAGER.value
        )
        assert not pm.is_admin
        assert pm.can_trade
        assert pm.can_manage_portfolio
        
        # Test trader
        trader = User(
            email="<EMAIL>",
            username="trader",
            role=UserRole.TRADER.value
        )
        assert not trader.is_admin
        assert trader.can_trade
        assert not trader.can_manage_portfolio
        
        # Test viewer
        viewer = User(
            email="<EMAIL>",
            username="viewer",
            role=UserRole.VIEWER.value
        )
        assert not viewer.is_admin
        assert not viewer.can_trade
        assert not viewer.can_manage_portfolio
    
    def test_password_hashing(self, db_session):
        """Test password hashing and verification."""
        user = User(email="<EMAIL>", username="test")
        password = "SecurePassword123!"
        
        user.set_password(password)
        
        assert user.hashed_password != password
        assert user.verify_password(password)
        assert not user.verify_password("WrongPassword")
        assert user.password_changed_at is not None


class TestUserProfileModel:
    """Test UserProfile model functionality."""
    
    def test_create_user_profile(self, db_session, sample_user):
        """Test creating a user profile."""
        profile = UserProfile(
            user_id=sample_user.id,
            company="Test Company",
            job_title="Portfolio Manager",
            phone="******-123-4567",
            timezone="America/New_York",
            language="en",
            currency_preference="USD"
        )
        
        db_session.add(profile)
        db_session.commit()
        
        assert profile.id is not None
        assert profile.user_id == sample_user.id
        assert profile.company == "Test Company"
        assert profile.display_name == sample_user.full_name
    
    def test_api_key_generation(self, db_session, sample_user):
        """Test API key generation."""
        profile = UserProfile(user_id=sample_user.id)
        
        api_key = profile.generate_api_key()
        
        assert api_key is not None
        assert len(api_key) > 20
        assert profile.api_key == api_key
        assert profile.api_key_created_at is not None


class TestBondModel:
    """Test Bond model functionality."""
    
    def test_create_bond(self, db_session):
        """Test creating a new bond."""
        bond = Bond(
            cusip="12345678A",
            isin="US1234567890",
            issuer_name="Test Corp",
            bond_type=BondType.CORPORATE.value,
            face_value=Decimal("1000.00"),
            coupon_rate=Decimal("5.25"),
            maturity_date=date.today() + timedelta(days=365*5),
            current_price=Decimal("102.50"),
            yield_to_maturity=Decimal("4.85"),
            duration=Decimal("4.2"),
            liquidity_score=Decimal("75.0"),
            liquidity_tier=LiquidityTier.TIER_2.value
        )
        
        db_session.add(bond)
        db_session.commit()
        
        assert bond.id is not None
        assert bond.cusip == "12345678A"
        assert bond.issuer_name == "Test Corp"
        assert bond.face_value == Decimal("1000.00")
        assert bond.is_active is True  # Default
    
    def test_time_to_maturity_property(self, db_session):
        """Test time to maturity calculation."""
        future_date = date.today() + timedelta(days=365)
        bond = Bond(
            cusip="12345678A",
            issuer_name="Test Corp",
            bond_type=BondType.CORPORATE.value,
            maturity_date=future_date
        )
        
        time_to_maturity = bond.time_to_maturity
        assert time_to_maturity is not None
        assert 360 <= time_to_maturity <= 370  # Approximately 1 year
    
    def test_investment_grade_property(self, db_session, sample_bond):
        """Test investment grade property."""
        # Add investment grade rating
        rating = BondRating(
            bond_id=sample_bond.id,
            rating_agency="sp",
            rating="A",
            rating_date=date.today()
        )
        db_session.add(rating)
        db_session.commit()
        
        assert sample_bond.is_investment_grade is True
        
        # Add high yield rating
        rating2 = BondRating(
            bond_id=sample_bond.id,
            rating_agency="sp",
            rating="BB",
            rating_date=date.today() + timedelta(days=1)  # More recent
        )
        db_session.add(rating2)
        db_session.commit()
        
        assert sample_bond.is_investment_grade is False


class TestBondRatingModel:
    """Test BondRating model functionality."""
    
    def test_create_bond_rating(self, db_session, sample_bond):
        """Test creating a bond rating."""
        rating = BondRating(
            bond_id=sample_bond.id,
            rating_agency="moody",
            rating="Aa2",
            outlook="Stable",
            rating_date=date.today(),
            rating_action="Upgrade"
        )
        
        db_session.add(rating)
        db_session.commit()
        
        assert rating.id is not None
        assert rating.bond_id == sample_bond.id
        assert rating.rating_agency == "moody"
        assert rating.rating == "Aa2"
    
    def test_investment_grade_property(self, db_session, sample_bond):
        """Test investment grade determination."""
        # Test investment grade ratings
        ig_ratings = [
            ("moody", "Aaa"), ("moody", "Aa1"), ("moody", "Baa3"),
            ("sp", "AAA"), ("sp", "AA+"), ("sp", "BBB-"),
            ("fitch", "AAA"), ("fitch", "A"), ("fitch", "BBB")
        ]
        
        for agency, rating_value in ig_ratings:
            rating = BondRating(
                bond_id=sample_bond.id,
                rating_agency=agency,
                rating=rating_value,
                rating_date=date.today()
            )
            assert rating.is_investment_grade is True
        
        # Test high yield ratings
        hy_ratings = [
            ("moody", "Ba1"), ("moody", "B3"), ("moody", "Caa1"),
            ("sp", "BB+"), ("sp", "B-"), ("sp", "CCC"),
            ("fitch", "BB"), ("fitch", "B"), ("fitch", "C")
        ]
        
        for agency, rating_value in hy_ratings:
            rating = BondRating(
                bond_id=sample_bond.id,
                rating_agency=agency,
                rating=rating_value,
                rating_date=date.today()
            )
            assert rating.is_investment_grade is False


class TestPortfolioModel:
    """Test Portfolio model functionality."""
    
    def test_create_portfolio(self, db_session, sample_user):
        """Test creating a new portfolio."""
        portfolio = Portfolio(
            user_id=sample_user.id,
            name="Test Portfolio",
            description="A test portfolio",
            portfolio_type=PortfolioType.INSTITUTIONAL.value,
            base_currency="USD",
            total_value=Decimal("10000000.00"),
            benchmark_name="Bloomberg Aggregate"
        )
        
        db_session.add(portfolio)
        db_session.commit()
        
        assert portfolio.id is not None
        assert portfolio.user_id == sample_user.id
        assert portfolio.name == "Test Portfolio"
        assert portfolio.status == PortfolioStatus.ACTIVE.value  # Default
        assert portfolio.is_compliant is True  # Default compliance status
    
    def test_calculate_total_value(self, db_session, sample_portfolio, sample_bond):
        """Test portfolio total value calculation."""
        # Add holdings
        holding1 = PortfolioHolding(
            portfolio_id=sample_portfolio.id,
            bond_id=sample_bond.id,
            quantity=Decimal("1000000.00"),
            average_cost=Decimal("100.00"),
            market_value=Decimal("1020000.00")
        )
        
        db_session.add(holding1)
        db_session.commit()
        
        # Update portfolio cash balance
        sample_portfolio.cash_balance = Decimal("500000.00")
        
        total_value = sample_portfolio.calculate_total_value()
        expected_value = Decimal("1020000.00") + Decimal("500000.00")
        
        assert total_value == expected_value
    
    def test_number_of_holdings_property(self, db_session, sample_portfolio, sample_bond):
        """Test number of holdings property."""
        assert sample_portfolio.number_of_holdings == 0
        
        # Add a holding
        holding = PortfolioHolding(
            portfolio_id=sample_portfolio.id,
            bond_id=sample_bond.id,
            quantity=Decimal("1000000.00"),
            average_cost=Decimal("100.00")
        )
        
        db_session.add(holding)
        db_session.commit()
        
        assert sample_portfolio.number_of_holdings == 1


class TestPortfolioHoldingModel:
    """Test PortfolioHolding model functionality."""
    
    def test_create_portfolio_holding(self, db_session, sample_portfolio, sample_bond):
        """Test creating a portfolio holding."""
        holding = PortfolioHolding(
            portfolio_id=sample_portfolio.id,
            bond_id=sample_bond.id,
            quantity=Decimal("1000000.00"),
            average_cost=Decimal("101.25")
        )
        
        db_session.add(holding)
        db_session.commit()
        
        assert holding.id is not None
        assert holding.portfolio_id == sample_portfolio.id
        assert holding.bond_id == sample_bond.id
        assert holding.is_active is True  # Default
    
    def test_calculate_book_value(self, db_session, sample_portfolio, sample_bond):
        """Test book value calculation."""
        holding = PortfolioHolding(
            portfolio_id=sample_portfolio.id,
            bond_id=sample_bond.id,
            quantity=Decimal("1000000.00"),  # $1M face value
            average_cost=Decimal("101.25")   # $101.25 per $100 face
        )
        
        holding.calculate_book_value()
        
        expected_book_value = Decimal("1000000.00") * Decimal("101.25") / 100
        assert holding.book_value == expected_book_value
    
    def test_update_market_value(self, db_session, sample_portfolio, sample_bond):
        """Test market value update."""
        holding = PortfolioHolding(
            portfolio_id=sample_portfolio.id,
            bond_id=sample_bond.id,
            quantity=Decimal("1000000.00"),
            average_cost=Decimal("101.25")
        )
        holding.calculate_book_value()
        
        current_price = Decimal("102.50")
        holding.update_market_value(current_price)
        
        expected_market_value = Decimal("1000000.00") * current_price / 100
        expected_unrealized_pnl = expected_market_value - holding.book_value
        
        assert holding.current_price == current_price
        assert holding.market_value == expected_market_value
        assert holding.unrealized_pnl == expected_unrealized_pnl
    
    def test_total_return_pct_property(self, db_session, sample_portfolio, sample_bond):
        """Test total return percentage calculation."""
        holding = PortfolioHolding(
            portfolio_id=sample_portfolio.id,
            bond_id=sample_bond.id,
            quantity=Decimal("1000000.00"),
            average_cost=Decimal("100.00")
        )
        holding.calculate_book_value()
        holding.update_market_value(Decimal("105.00"))
        
        total_return = holding.total_return_pct
        expected_return = Decimal("5.00")  # 5% gain
        
        assert total_return == expected_return


class TestTradeModel:
    """Test Trade model functionality."""
    
    def test_create_trade(self, db_session, sample_user, sample_portfolio, sample_bond):
        """Test creating a new trade."""
        trade = Trade(
            user_id=sample_user.id,
            portfolio_id=sample_portfolio.id,
            bond_id=sample_bond.id,
            trade_id="TRD-20240101-TEST001",
            side=OrderSide.BUY.value,
            quantity=Decimal("500000.00"),
            price=Decimal("102.00"),
            gross_amount=Decimal("510000.00")
        )
        
        db_session.add(trade)
        db_session.commit()
        
        assert trade.id is not None
        assert trade.trade_id == "TRD-20240101-TEST001"
        assert trade.status == TradeStatus.PENDING.value  # Default
        assert trade.is_buy is True
        assert trade.is_sell is False
    
    def test_calculate_net_amount(self, db_session, sample_user, sample_portfolio, sample_bond):
        """Test net amount calculation."""
        # Test buy trade
        buy_trade = Trade(
            user_id=sample_user.id,
            portfolio_id=sample_portfolio.id,
            bond_id=sample_bond.id,
            trade_id="TRD-BUY-001",
            side=OrderSide.BUY.value,
            quantity=Decimal("500000.00"),
            price=Decimal("102.00"),
            gross_amount=Decimal("510000.00"),
            accrued_interest=Decimal("5000.00"),
            commission=Decimal("500.00"),
            fees=Decimal("100.00")
        )
        
        buy_trade.calculate_net_amount()
        expected_net = Decimal("510000.00") + Decimal("5000.00") + Decimal("500.00") + Decimal("100.00")
        assert buy_trade.net_amount == expected_net
        
        # Test sell trade
        sell_trade = Trade(
            user_id=sample_user.id,
            portfolio_id=sample_portfolio.id,
            bond_id=sample_bond.id,
            trade_id="TRD-SELL-001",
            side=OrderSide.SELL.value,
            quantity=Decimal("500000.00"),
            price=Decimal("102.00"),
            gross_amount=Decimal("510000.00"),
            accrued_interest=Decimal("5000.00"),
            commission=Decimal("500.00"),
            fees=Decimal("100.00")
        )
        
        sell_trade.calculate_net_amount()
        expected_net = Decimal("510000.00") + Decimal("5000.00") - Decimal("500.00") - Decimal("100.00")
        assert sell_trade.net_amount == expected_net


class TestTradeOrderModel:
    """Test TradeOrder model functionality."""
    
    def test_create_trade_order(self, db_session, sample_trade):
        """Test creating a trade order."""
        order = TradeOrder(
            trade_id=sample_trade.id,
            order_id="ORD-20240101-TEST001",
            order_type=OrderType.LIMIT.value,
            side=OrderSide.BUY.value,
            quantity=Decimal("500000.00"),
            price=Decimal("102.00"),
            remaining_quantity=Decimal("500000.00")
        )
        
        db_session.add(order)
        db_session.commit()
        
        assert order.id is not None
        assert order.order_id == "ORD-20240101-TEST001"
        assert order.status == OrderStatus.PENDING.value  # Default
        assert order.is_filled is False
    
    def test_fill_percentage_property(self, db_session, sample_trade):
        """Test fill percentage calculation."""
        order = TradeOrder(
            trade_id=sample_trade.id,
            order_id="ORD-TEST-001",
            order_type=OrderType.MARKET.value,
            side=OrderSide.BUY.value,
            quantity=Decimal("1000000.00"),
            filled_quantity=Decimal("250000.00")
        )
        
        fill_percentage = order.fill_percentage
        expected_percentage = Decimal("25.0")  # 25% filled
        
        assert fill_percentage == expected_percentage
        
        # Test fully filled order
        order.filled_quantity = order.quantity
        order.status = OrderStatus.FILLED.value
        
        assert order.fill_percentage == Decimal("100.0")
        assert order.is_filled is True
