<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="LiquidBond - AI-Powered Bond Liquidity Optimization Platform. Revolutionary quantum cognition AI that optimizes bond portfolio liquidity by finding liquid alternatives to illiquid bonds." />
    <meta name="keywords" content="bonds, liquidity, AI, quantum cognition, portfolio optimization, trading, fixed income" />
    <meta name="author" content="HectorTa1989" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://liquidbond.ai/" />
    <meta property="og:title" content="LiquidBond - AI-Powered Bond Liquidity Optimization" />
    <meta property="og:description" content="Revolutionary quantum cognition AI platform that optimizes bond portfolio liquidity by finding liquid alternatives to illiquid bonds, reducing trading costs by 30-50%." />
    <meta property="og:image" content="/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://liquidbond.ai/" />
    <meta property="twitter:title" content="LiquidBond - AI-Powered Bond Liquidity Optimization" />
    <meta property="twitter:description" content="Revolutionary quantum cognition AI platform that optimizes bond portfolio liquidity by finding liquid alternatives to illiquid bonds." />
    <meta property="twitter:image" content="/twitter-image.png" />

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet" />
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    
    <title>LiquidBond - AI-Powered Bond Liquidity Optimization Platform</title>
    
    <style>
      /* Critical CSS for initial load */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
        line-height: 1.6;
        color: #1a1a1a;
        background-color: #ffffff;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      #root {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }
      
      /* Loading spinner */
      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #ffffff;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: white;
        margin-top: 20px;
        font-size: 18px;
        font-weight: 500;
      }
      
      /* Error boundary styles */
      .error-boundary {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
        text-align: center;
        background-color: #f8f9fa;
      }
      
      .error-boundary h1 {
        color: #dc3545;
        margin-bottom: 16px;
        font-size: 24px;
      }
      
      .error-boundary p {
        color: #6c757d;
        margin-bottom: 24px;
        max-width: 500px;
      }
      
      .error-boundary button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 16px;
        transition: background-color 0.2s;
      }
      
      .error-boundary button:hover {
        background-color: #0056b3;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading-container">
        <div>
          <div class="loading-spinner"></div>
          <div class="loading-text">Loading LiquidBond...</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
