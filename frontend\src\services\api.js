import axios from 'axios'
import toast from 'react-hot-toast'

// API base configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add timestamp to prevent caching
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      }
    }
    
    // Add auth token if available
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // Log request in development
    if (import.meta.env.DEV) {
      console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, config.data || config.params)
    }
    
    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    // Log response in development
    if (import.meta.env.DEV) {
      console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url}`, response.data)
    }
    
    return response.data
  },
  (error) => {
    // Log error in development
    if (import.meta.env.DEV) {
      console.error(`❌ ${error.config?.method?.toUpperCase()} ${error.config?.url}`, error.response?.data || error.message)
    }
    
    // Handle different error types
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          toast.error(data.detail || 'Bad request')
          break
        case 401:
          // Don't show toast for 401 - handled by auth service
          break
        case 403:
          toast.error('Access denied')
          break
        case 404:
          toast.error('Resource not found')
          break
        case 422:
          // Validation errors
          if (data.detail && Array.isArray(data.detail)) {
            data.detail.forEach(err => {
              toast.error(`${err.loc?.join('.')} - ${err.msg}`)
            })
          } else {
            toast.error(data.detail || 'Validation error')
          }
          break
        case 429:
          toast.error('Too many requests. Please try again later.')
          break
        case 500:
          toast.error('Server error. Please try again later.')
          break
        default:
          toast.error(data.detail || 'An error occurred')
      }
    } else if (error.request) {
      // Network error
      toast.error('Network error. Please check your connection.')
    } else {
      // Other error
      toast.error('An unexpected error occurred')
    }
    
    return Promise.reject(error)
  }
)

// API endpoints
export const endpoints = {
  // Authentication
  auth: {
    login: '/auth/login',
    logout: '/auth/logout',
    refresh: '/auth/refresh',
    me: '/auth/me',
    register: '/auth/register',
    changePassword: '/auth/change-password',
    profile: '/auth/profile',
  },
  
  // Bonds
  bonds: {
    search: '/bonds/search',
    detail: (cusip) => `/bonds/${cusip}`,
    similar: '/bonds/similar',
    liquidityAnalysis: (cusip) => `/bonds/${cusip}/liquidity-analysis`,
    priceHistory: (cusip) => `/bonds/${cusip}/price-history`,
  },
  
  // Portfolio
  portfolio: {
    list: '/portfolio',
    detail: (id) => `/portfolio/${id}`,
    create: '/portfolio',
    update: (id) => `/portfolio/${id}`,
    delete: (id) => `/portfolio/${id}`,
    holdings: (id) => `/portfolio/${id}/holdings`,
    performance: (id) => `/portfolio/${id}/performance`,
    riskMetrics: (id) => `/portfolio/${id}/risk-metrics`,
    optimization: (id) => `/portfolio/${id}/optimization`,
  },
  
  // Trading
  trading: {
    orders: '/trading/orders',
    trades: '/trading/trades',
    execute: '/trading/execute',
    cancel: (orderId) => `/trading/orders/${orderId}/cancel`,
    history: '/trading/history',
  },
  
  // Analytics
  analytics: {
    dashboard: '/analytics/dashboard',
    performance: '/analytics/performance',
    risk: '/analytics/risk',
    liquidity: '/analytics/liquidity',
    market: '/analytics/market',
  },
}

// Utility functions for common API patterns
export const apiUtils = {
  // GET request with query parameters
  get: (url, params = {}) => api.get(url, { params }),
  
  // POST request
  post: (url, data = {}) => api.post(url, data),
  
  // PUT request
  put: (url, data = {}) => api.put(url, data),
  
  // DELETE request
  delete: (url) => api.delete(url),
  
  // PATCH request
  patch: (url, data = {}) => api.patch(url, data),
  
  // Upload file
  upload: (url, file, onProgress) => {
    const formData = new FormData()
    formData.append('file', file)
    
    return api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          onProgress(percentCompleted)
        }
      },
    })
  },
  
  // Download file
  download: async (url, filename) => {
    try {
      const response = await api.get(url, {
        responseType: 'blob',
      })
      
      const blob = new Blob([response])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(downloadUrl)
    } catch (error) {
      toast.error('Download failed')
      throw error
    }
  },
}

// React Query helpers
export const queryKeys = {
  // Authentication
  currentUser: ['auth', 'currentUser'],
  
  // Bonds
  bonds: (filters) => ['bonds', 'search', filters],
  bond: (cusip) => ['bonds', 'detail', cusip],
  similarBonds: (cusip, params) => ['bonds', 'similar', cusip, params],
  bondLiquidity: (cusip) => ['bonds', 'liquidity', cusip],
  bondPrices: (cusip, days) => ['bonds', 'prices', cusip, days],
  
  // Portfolio
  portfolios: ['portfolios'],
  portfolio: (id) => ['portfolios', id],
  portfolioHoldings: (id) => ['portfolios', id, 'holdings'],
  portfolioPerformance: (id) => ['portfolios', id, 'performance'],
  portfolioRisk: (id) => ['portfolios', id, 'risk'],
  
  // Trading
  orders: (filters) => ['trading', 'orders', filters],
  trades: (filters) => ['trading', 'trades', filters],
  tradeHistory: (filters) => ['trading', 'history', filters],
  
  // Analytics
  dashboardData: ['analytics', 'dashboard'],
  performanceData: (params) => ['analytics', 'performance', params],
  riskData: (params) => ['analytics', 'risk', params],
  liquidityData: (params) => ['analytics', 'liquidity', params],
  marketData: (params) => ['analytics', 'market', params],
}

// Export default api instance
export default api
