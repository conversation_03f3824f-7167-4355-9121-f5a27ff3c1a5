"""
Validation utilities for LiquidBond application
"""

import re
from datetime import datetime, date
from decimal import Decimal, InvalidOperation
from typing import Dict, Any, List, Optional
from email_validator import validate_email as email_validate, EmailNotValidError


class ValidationError(Exception):
    """Custom validation error"""
    def __init__(self, message: str, field: Optional[str] = None):
        self.message = message
        self.field = field
        super().__init__(self.message)


def validate_email(email: str) -> bool:
    """Validate email address format"""
    try:
        email_validate(email)
        return True
    except EmailNotValidError:
        return False


def validate_password(password: str) -> Dict[str, Any]:
    """Validate password strength and return detailed feedback"""
    result = {
        'is_valid': True,
        'errors': [],
        'strength_score': 0
    }
    
    if len(password) < 8:
        result['errors'].append("Password must be at least 8 characters long")
        result['is_valid'] = False
    else:
        result['strength_score'] += 1
    
    if len(password) >= 12:
        result['strength_score'] += 1
    
    if not re.search(r'[a-z]', password):
        result['errors'].append("Password must contain at least one lowercase letter")
        result['is_valid'] = False
    else:
        result['strength_score'] += 1
    
    if not re.search(r'[A-Z]', password):
        result['errors'].append("Password must contain at least one uppercase letter")
        result['is_valid'] = False
    else:
        result['strength_score'] += 1
    
    if not re.search(r'\d', password):
        result['errors'].append("Password must contain at least one number")
        result['is_valid'] = False
    else:
        result['strength_score'] += 1
    
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        result['errors'].append("Password must contain at least one special character")
        result['is_valid'] = False
    else:
        result['strength_score'] += 1
    
    # Check for common patterns
    if re.search(r'(.)\1{2,}', password):  # Repeated characters
        result['errors'].append("Password should not contain repeated characters")
        result['strength_score'] -= 1
    
    if re.search(r'(012|123|234|345|456|567|678|789|890)', password):
        result['errors'].append("Password should not contain sequential numbers")
        result['strength_score'] -= 1
    
    if re.search(r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)', password.lower()):
        result['errors'].append("Password should not contain sequential letters")
        result['strength_score'] -= 1
    
    # Common passwords check (simplified)
    common_passwords = [
        'password', '123456', '123456789', 'qwerty', 'abc123',
        'password123', 'admin', 'letmein', 'welcome', 'monkey'
    ]
    
    if password.lower() in common_passwords:
        result['errors'].append("Password is too common")
        result['is_valid'] = False
        result['strength_score'] = 0
    
    result['strength_score'] = max(0, min(6, result['strength_score']))
    
    return result


def validate_phone(phone: str) -> bool:
    """Validate phone number format"""
    if not phone:
        return False
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    # Check if it's a valid length (10-15 digits)
    if len(digits_only) < 10 or len(digits_only) > 15:
        return False
    
    # Basic format validation
    phone_pattern = r'^[\+]?[1-9][\d]{0,3}[-.\s]?[\d]{3,4}[-.\s]?[\d]{3,4}[-.\s]?[\d]{0,4}$'
    return bool(re.match(phone_pattern, phone))


def validate_bond_data(bond_data: Dict[str, Any]) -> List[str]:
    """Validate bond data and return list of errors"""
    errors = []
    
    # Required fields
    required_fields = ['cusip', 'issuer_name', 'bond_type', 'maturity_date']
    for field in required_fields:
        if not bond_data.get(field):
            errors.append(f"Missing required field: {field}")
    
    # CUSIP validation
    cusip = bond_data.get('cusip')
    if cusip:
        from .helpers import validate_cusip
        if not validate_cusip(cusip):
            errors.append("Invalid CUSIP format or check digit")
    
    # ISIN validation (if provided)
    isin = bond_data.get('isin')
    if isin:
        from .helpers import validate_isin
        if not validate_isin(isin):
            errors.append("Invalid ISIN format or check digit")
    
    # Bond type validation
    valid_bond_types = [
        'corporate', 'government', 'municipal', 'treasury', 
        'agency', 'convertible', 'high_yield', 'investment_grade'
    ]
    bond_type = bond_data.get('bond_type')
    if bond_type and bond_type not in valid_bond_types:
        errors.append(f"Invalid bond type. Must be one of: {', '.join(valid_bond_types)}")
    
    # Maturity date validation
    maturity_date = bond_data.get('maturity_date')
    if maturity_date:
        try:
            if isinstance(maturity_date, str):
                maturity_date = datetime.strptime(maturity_date, '%Y-%m-%d').date()
            
            if maturity_date <= date.today():
                errors.append("Maturity date must be in the future")
        except (ValueError, TypeError):
            errors.append("Invalid maturity date format")
    
    # Numerical field validations
    numerical_fields = {
        'face_value': {'min': 0, 'max': 1000000000},
        'coupon_rate': {'min': 0, 'max': 50},
        'current_price': {'min': 0, 'max': 1000},
        'yield_to_maturity': {'min': 0, 'max': 50},
        'duration': {'min': 0, 'max': 50},
        'liquidity_score': {'min': 0, 'max': 100},
        'credit_spread': {'min': 0, 'max': 10000}
    }
    
    for field, constraints in numerical_fields.items():
        value = bond_data.get(field)
        if value is not None:
            try:
                if isinstance(value, str):
                    value = Decimal(value)
                elif not isinstance(value, Decimal):
                    value = Decimal(str(value))
                
                if value < constraints['min'] or value > constraints['max']:
                    errors.append(f"{field} must be between {constraints['min']} and {constraints['max']}")
            except (InvalidOperation, ValueError):
                errors.append(f"Invalid {field} format")
    
    # Currency validation
    currency = bond_data.get('currency')
    if currency:
        valid_currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF']
        if currency not in valid_currencies:
            errors.append(f"Invalid currency. Must be one of: {', '.join(valid_currencies)}")
    
    # Country code validation
    country = bond_data.get('country')
    if country and len(country) != 3:
        errors.append("Country code must be 3 characters (ISO 3166-1 alpha-3)")
    
    return errors


def validate_portfolio_data(portfolio_data: Dict[str, Any]) -> List[str]:
    """Validate portfolio data and return list of errors"""
    errors = []
    
    # Required fields
    required_fields = ['name', 'portfolio_type']
    for field in required_fields:
        if not portfolio_data.get(field):
            errors.append(f"Missing required field: {field}")
    
    # Name validation
    name = portfolio_data.get('name')
    if name and (len(name) < 1 or len(name) > 255):
        errors.append("Portfolio name must be between 1 and 255 characters")
    
    # Portfolio type validation
    valid_portfolio_types = [
        'institutional', 'pension', 'insurance', 'mutual_fund',
        'hedge_fund', 'sovereign', 'corporate', 'personal'
    ]
    portfolio_type = portfolio_data.get('portfolio_type')
    if portfolio_type and portfolio_type not in valid_portfolio_types:
        errors.append(f"Invalid portfolio type. Must be one of: {', '.join(valid_portfolio_types)}")
    
    # Currency validation
    base_currency = portfolio_data.get('base_currency')
    if base_currency:
        valid_currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF']
        if base_currency not in valid_currencies:
            errors.append(f"Invalid base currency. Must be one of: {', '.join(valid_currencies)}")
    
    # Numerical constraints
    max_position_size = portfolio_data.get('max_position_size')
    if max_position_size is not None:
        try:
            if isinstance(max_position_size, str):
                max_position_size = Decimal(max_position_size)
            
            if max_position_size <= 0 or max_position_size > 100:
                errors.append("Max position size must be between 0 and 100 percent")
        except (InvalidOperation, ValueError):
            errors.append("Invalid max position size format")
    
    max_duration = portfolio_data.get('max_duration')
    if max_duration is not None:
        try:
            if isinstance(max_duration, str):
                max_duration = Decimal(max_duration)
            
            if max_duration <= 0 or max_duration > 50:
                errors.append("Max duration must be between 0 and 50 years")
        except (InvalidOperation, ValueError):
            errors.append("Invalid max duration format")
    
    # Credit rating validation
    min_credit_rating = portfolio_data.get('min_credit_rating')
    if min_credit_rating:
        valid_ratings = [
            'AAA', 'AA+', 'AA', 'AA-', 'A+', 'A', 'A-',
            'BBB+', 'BBB', 'BBB-', 'BB+', 'BB', 'BB-',
            'B+', 'B', 'B-', 'CCC', 'CC', 'C', 'D'
        ]
        if min_credit_rating not in valid_ratings:
            errors.append(f"Invalid credit rating. Must be one of: {', '.join(valid_ratings)}")
    
    return errors


def validate_trade_data(trade_data: Dict[str, Any]) -> List[str]:
    """Validate trade data and return list of errors"""
    errors = []
    
    # Required fields
    required_fields = ['portfolio_id', 'bond_cusip', 'side', 'quantity']
    for field in required_fields:
        if not trade_data.get(field):
            errors.append(f"Missing required field: {field}")
    
    # Side validation
    side = trade_data.get('side')
    if side and side not in ['buy', 'sell']:
        errors.append("Side must be 'buy' or 'sell'")
    
    # Quantity validation
    quantity = trade_data.get('quantity')
    if quantity is not None:
        try:
            if isinstance(quantity, str):
                quantity = Decimal(quantity)
            
            if quantity <= 0:
                errors.append("Quantity must be greater than 0")
            elif quantity > Decimal('1000000000'):  # $1B max
                errors.append("Quantity exceeds maximum allowed")
        except (InvalidOperation, ValueError):
            errors.append("Invalid quantity format")
    
    # Order type validation
    order_type = trade_data.get('order_type')
    if order_type:
        valid_order_types = ['market', 'limit', 'stop', 'stop_limit', 'ioc', 'fok']
        if order_type not in valid_order_types:
            errors.append(f"Invalid order type. Must be one of: {', '.join(valid_order_types)}")
    
    # Price validations
    limit_price = trade_data.get('limit_price')
    if limit_price is not None:
        try:
            if isinstance(limit_price, str):
                limit_price = Decimal(limit_price)
            
            if limit_price <= 0 or limit_price > 1000:
                errors.append("Limit price must be between 0 and 1000")
        except (InvalidOperation, ValueError):
            errors.append("Invalid limit price format")
    
    stop_price = trade_data.get('stop_price')
    if stop_price is not None:
        try:
            if isinstance(stop_price, str):
                stop_price = Decimal(stop_price)
            
            if stop_price <= 0 or stop_price > 1000:
                errors.append("Stop price must be between 0 and 1000")
        except (InvalidOperation, ValueError):
            errors.append("Invalid stop price format")
    
    # Order type specific validations
    if order_type == 'limit' and not limit_price:
        errors.append("Limit price is required for limit orders")
    
    if order_type in ['stop', 'stop_limit'] and not stop_price:
        errors.append("Stop price is required for stop orders")
    
    if order_type == 'stop_limit' and not limit_price:
        errors.append("Limit price is required for stop limit orders")
    
    return errors


def validate_user_data(user_data: Dict[str, Any]) -> List[str]:
    """Validate user registration/update data"""
    errors = []
    
    # Email validation
    email = user_data.get('email')
    if email and not validate_email(email):
        errors.append("Invalid email format")
    
    # Username validation
    username = user_data.get('username')
    if username:
        if len(username) < 3 or len(username) > 50:
            errors.append("Username must be between 3 and 50 characters")
        
        if not re.match(r'^[a-zA-Z0-9_.-]+$', username):
            errors.append("Username can only contain letters, numbers, dots, hyphens, and underscores")
    
    # Password validation
    password = user_data.get('password')
    if password:
        password_result = validate_password(password)
        if not password_result['is_valid']:
            errors.extend(password_result['errors'])
    
    # Name validations
    for field in ['first_name', 'last_name']:
        value = user_data.get(field)
        if value:
            if len(value) < 1 or len(value) > 100:
                errors.append(f"{field.replace('_', ' ').title()} must be between 1 and 100 characters")
            
            if not re.match(r'^[a-zA-Z\s\'-]+$', value):
                errors.append(f"{field.replace('_', ' ').title()} can only contain letters, spaces, hyphens, and apostrophes")
    
    # Phone validation
    phone = user_data.get('phone')
    if phone and not validate_phone(phone):
        errors.append("Invalid phone number format")
    
    # Role validation
    role = user_data.get('role')
    if role:
        valid_roles = ['admin', 'portfolio_manager', 'trader', 'risk_manager', 'analyst', 'viewer']
        if role not in valid_roles:
            errors.append(f"Invalid role. Must be one of: {', '.join(valid_roles)}")
    
    return errors
