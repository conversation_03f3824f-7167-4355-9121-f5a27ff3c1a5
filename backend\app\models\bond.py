"""
Bond-related database models
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional, List
from enum import Enum

from sqlalchemy import Column, Integer, String, DateTime, Date, Numeric, Boolean, Text, ForeignKey, Index
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid

from app.database import Base


class BondType(str, Enum):
    """Bond type enumeration"""
    CORPORATE = "corporate"
    GOVERNMENT = "government"
    MUNICIPAL = "municipal"
    TREASURY = "treasury"
    AGENCY = "agency"
    CONVERTIBLE = "convertible"
    HIGH_YIELD = "high_yield"
    INVESTMENT_GRADE = "investment_grade"


class RatingAgency(str, Enum):
    """Credit rating agency enumeration"""
    MOODY = "moody"
    SP = "sp"
    FITCH = "fitch"


class LiquidityTier(str, Enum):
    """Bond liquidity tier classification"""
    TIER_1 = "tier_1"  # Highly liquid
    TIER_2 = "tier_2"  # Moderately liquid
    TIER_3 = "tier_3"  # Illiquid
    TIER_4 = "tier_4"  # Highly illiquid


class Bond(Base):
    """Bond master data model"""
    __tablename__ = "bonds"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    cusip = Column(String(9), unique=True, index=True, nullable=False)
    isin = Column(String(12), unique=True, index=True)
    symbol = Column(String(20), index=True)
    
    # Basic Information
    issuer_name = Column(String(255), nullable=False, index=True)
    bond_type = Column(String(50), nullable=False, index=True)
    description = Column(Text)
    
    # Financial Details
    face_value = Column(Numeric(15, 2), default=1000.00)
    coupon_rate = Column(Numeric(8, 4))  # Annual coupon rate as percentage
    maturity_date = Column(Date, nullable=False, index=True)
    issue_date = Column(Date, index=True)
    
    # Market Data
    current_price = Column(Numeric(10, 4))
    yield_to_maturity = Column(Numeric(8, 4))
    duration = Column(Numeric(8, 4))
    convexity = Column(Numeric(10, 6))
    
    # Liquidity Metrics
    liquidity_score = Column(Numeric(5, 2))  # 0-100 scale
    liquidity_tier = Column(String(20), index=True)
    avg_daily_volume = Column(Numeric(15, 2))
    bid_ask_spread = Column(Numeric(8, 4))
    
    # Risk Metrics
    credit_spread = Column(Numeric(8, 4))
    option_adjusted_spread = Column(Numeric(8, 4))
    
    # Classification
    sector = Column(String(100), index=True)
    industry = Column(String(100), index=True)
    country = Column(String(3), index=True)  # ISO country code
    currency = Column(String(3), default="USD", index=True)
    
    # Features for ML
    features = Column(JSONB)  # Store computed features for ML models
    
    # Status
    is_active = Column(Boolean, default=True, index=True)
    is_callable = Column(Boolean, default=False)
    is_puttable = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    prices = relationship("BondPrice", back_populates="bond", cascade="all, delete-orphan")
    ratings = relationship("BondRating", back_populates="bond", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_bond_liquidity', 'liquidity_tier', 'liquidity_score'),
        Index('idx_bond_maturity_sector', 'maturity_date', 'sector'),
        Index('idx_bond_type_rating', 'bond_type', 'sector'),
    )
    
    def __repr__(self):
        return f"<Bond(cusip='{self.cusip}', issuer='{self.issuer_name}')>"
    
    @property
    def time_to_maturity(self) -> Optional[int]:
        """Calculate days to maturity"""
        if self.maturity_date:
            return (self.maturity_date - date.today()).days
        return None
    
    @property
    def is_investment_grade(self) -> Optional[bool]:
        """Check if bond is investment grade based on latest rating"""
        latest_rating = max(self.ratings, key=lambda r: r.rating_date, default=None)
        if latest_rating:
            return latest_rating.is_investment_grade
        return None


class BondPrice(Base):
    """Bond price history model"""
    __tablename__ = "bond_prices"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    bond_id = Column(UUID(as_uuid=True), ForeignKey("bonds.id"), nullable=False)
    
    # Price Data
    price_date = Column(Date, nullable=False, index=True)
    price = Column(Numeric(10, 4), nullable=False)
    yield_value = Column(Numeric(8, 4))
    
    # Volume and Spread
    volume = Column(Numeric(15, 2))
    bid_price = Column(Numeric(10, 4))
    ask_price = Column(Numeric(10, 4))
    bid_ask_spread = Column(Numeric(8, 4))
    
    # Market Data Source
    data_source = Column(String(50), index=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    bond = relationship("Bond", back_populates="prices")
    
    # Indexes
    __table_args__ = (
        Index('idx_bond_price_date', 'bond_id', 'price_date'),
    )
    
    def __repr__(self):
        return f"<BondPrice(bond_id='{self.bond_id}', date='{self.price_date}', price={self.price})>"


class BondRating(Base):
    """Bond credit rating model"""
    __tablename__ = "bond_ratings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    bond_id = Column(UUID(as_uuid=True), ForeignKey("bonds.id"), nullable=False)
    
    # Rating Information
    rating_agency = Column(String(20), nullable=False, index=True)
    rating = Column(String(10), nullable=False, index=True)
    outlook = Column(String(20))  # Positive, Negative, Stable, Developing
    
    # Rating Details
    rating_date = Column(Date, nullable=False, index=True)
    previous_rating = Column(String(10))
    rating_action = Column(String(20))  # Upgrade, Downgrade, Affirmed, New
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    bond = relationship("Bond", back_populates="ratings")
    
    # Indexes
    __table_args__ = (
        Index('idx_bond_rating_date', 'bond_id', 'rating_date'),
        Index('idx_rating_agency_rating', 'rating_agency', 'rating'),
    )
    
    @property
    def is_investment_grade(self) -> bool:
        """Check if rating is investment grade"""
        investment_grade_ratings = {
            'moody': ['Aaa', 'Aa1', 'Aa2', 'Aa3', 'A1', 'A2', 'A3', 'Baa1', 'Baa2', 'Baa3'],
            'sp': ['AAA', 'AA+', 'AA', 'AA-', 'A+', 'A', 'A-', 'BBB+', 'BBB', 'BBB-'],
            'fitch': ['AAA', 'AA+', 'AA', 'AA-', 'A+', 'A', 'A-', 'BBB+', 'BBB', 'BBB-']
        }
        
        agency_ratings = investment_grade_ratings.get(self.rating_agency.lower(), [])
        return self.rating in agency_ratings
    
    def __repr__(self):
        return f"<BondRating(bond_id='{self.bond_id}', agency='{self.rating_agency}', rating='{self.rating}')>"
