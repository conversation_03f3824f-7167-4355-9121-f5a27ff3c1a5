"""
Quantum Cognition Engine for Bond Analysis
Implements quantum-inspired cognitive models for bond similarity and decision making
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import asyncio
import logging
from datetime import datetime, timedelta

import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.metrics.pairwise import cosine_similarity

logger = logging.getLogger(__name__)


class CognitionState(Enum):
    """Quantum cognition states for bond analysis"""
    SUPERPOSITION = "superposition"
    ENTANGLED = "entangled"
    COLLAPSED = "collapsed"
    INTERFERENCE = "interference"


@dataclass
class QuantumBondState:
    """Represents a bond in quantum cognitive space"""
    bond_id: str
    cusip: str
    state_vector: np.ndarray
    probability_amplitude: complex
    entanglement_partners: List[str]
    coherence_time: float
    measurement_history: List[Dict[str, Any]]


class QuantumCognitionNetwork(nn.Module):
    """Neural network implementing quantum cognition principles"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 256, output_dim: int = 128):
        super().__init__()
        
        # Quantum-inspired layers
        self.superposition_layer = nn.Linear(input_dim, hidden_dim)
        self.entanglement_layer = nn.MultiheadAttention(hidden_dim, num_heads=8)
        self.interference_layer = nn.Linear(hidden_dim, hidden_dim)
        self.collapse_layer = nn.Linear(hidden_dim, output_dim)
        
        # Quantum gates simulation
        self.hadamard_gate = nn.Parameter(torch.tensor([[1, 1], [1, -1]], dtype=torch.float32) / np.sqrt(2))
        self.pauli_x = nn.Parameter(torch.tensor([[0, 1], [1, 0]], dtype=torch.float32))
        self.pauli_z = nn.Parameter(torch.tensor([[1, 0], [0, -1]], dtype=torch.float32))
        
        # Normalization layers
        self.layer_norm1 = nn.LayerNorm(hidden_dim)
        self.layer_norm2 = nn.LayerNorm(hidden_dim)
        
        # Dropout for regularization
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass through quantum cognition network"""
        batch_size = x.size(0)
        
        # Superposition: Create quantum superposition of bond features
        superposition = torch.tanh(self.superposition_layer(x))
        superposition = self.layer_norm1(superposition)
        
        # Entanglement: Multi-head attention for bond relationships
        entangled, attention_weights = self.entanglement_layer(
            superposition.unsqueeze(1),
            superposition.unsqueeze(1),
            superposition.unsqueeze(1)
        )
        entangled = entangled.squeeze(1)
        entangled = self.layer_norm2(entangled + superposition)  # Residual connection
        
        # Interference: Quantum interference effects
        interference = torch.relu(self.interference_layer(entangled))
        interference = self.dropout(interference)
        
        # Collapse: Measurement/observation collapses quantum state
        collapsed_state = self.collapse_layer(interference)
        
        # Normalize to unit sphere (quantum state constraint)
        collapsed_state = F.normalize(collapsed_state, p=2, dim=1)
        
        return collapsed_state, attention_weights.squeeze(1)


class QuantumCognitionEngine:
    """Main quantum cognition engine for bond analysis"""
    
    def __init__(self, feature_dim: int = 50, hidden_dim: int = 256, output_dim: int = 128):
        self.feature_dim = feature_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        
        # Initialize quantum network
        self.quantum_network = QuantumCognitionNetwork(feature_dim, hidden_dim, output_dim)
        self.optimizer = torch.optim.Adam(self.quantum_network.parameters(), lr=0.001)
        
        # Feature processing
        self.scaler = StandardScaler()
        self.pca = PCA(n_components=feature_dim)
        
        # Quantum state storage
        self.bond_states: Dict[str, QuantumBondState] = {}
        self.entanglement_matrix = np.zeros((1000, 1000))  # Max 1000 bonds initially
        
        # Cognitive parameters
        self.coherence_decay = 0.95  # Quantum coherence decay rate
        self.entanglement_threshold = 0.7  # Threshold for entanglement
        self.measurement_noise = 0.01  # Quantum measurement noise
        
        self.is_initialized = False
        
    async def initialize(self):
        """Initialize the quantum cognition engine"""
        try:
            logger.info("Initializing Quantum Cognition Engine...")
            
            # Load pre-trained weights if available
            try:
                checkpoint = torch.load("models/quantum_cognition_model.pth", map_location='cpu')
                self.quantum_network.load_state_dict(checkpoint['model_state_dict'])
                self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                logger.info("Loaded pre-trained quantum cognition model")
            except FileNotFoundError:
                logger.info("No pre-trained model found, using random initialization")
            
            self.is_initialized = True
            logger.info("✅ Quantum Cognition Engine initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Quantum Cognition Engine: {e}")
            raise
    
    def extract_bond_features(self, bond_data: Dict[str, Any]) -> np.ndarray:
        """Extract quantum-relevant features from bond data"""
        features = []
        
        # Financial features
        features.extend([
            bond_data.get('coupon_rate', 0) / 100,
            bond_data.get('yield_to_maturity', 0) / 100,
            bond_data.get('duration', 0) / 10,
            bond_data.get('convexity', 0) / 100,
            bond_data.get('current_price', 100) / 100,
        ])
        
        # Risk features
        features.extend([
            bond_data.get('credit_spread', 0) / 1000,
            bond_data.get('liquidity_score', 50) / 100,
            bond_data.get('bid_ask_spread', 0) / 100,
        ])
        
        # Time features
        maturity_years = bond_data.get('time_to_maturity', 365) / 365
        features.append(min(maturity_years / 30, 1.0))  # Normalize to 30 years max
        
        # Categorical features (one-hot encoded)
        bond_type = bond_data.get('bond_type', 'corporate')
        type_encoding = [0] * 5
        type_map = {'corporate': 0, 'government': 1, 'municipal': 2, 'treasury': 3, 'agency': 4}
        if bond_type in type_map:
            type_encoding[type_map[bond_type]] = 1
        features.extend(type_encoding)
        
        # Sector encoding
        sector = bond_data.get('sector', 'other')
        sector_encoding = [0] * 10  # Top 10 sectors
        sector_map = {
            'financial': 0, 'technology': 1, 'healthcare': 2, 'energy': 3,
            'utilities': 4, 'industrials': 5, 'consumer': 6, 'telecom': 7,
            'materials': 8, 'other': 9
        }
        if sector in sector_map:
            sector_encoding[sector_map[sector]] = 1
        features.extend(sector_encoding)
        
        # Rating encoding
        rating = bond_data.get('credit_rating', 'BBB')
        rating_score = self._rating_to_score(rating)
        features.append(rating_score)
        
        # Market features
        features.extend([
            bond_data.get('avg_daily_volume', 0) / 1000000,  # Normalize to millions
            bond_data.get('market_cap', 0) / **********,     # Normalize to billions
        ])
        
        # Pad or truncate to feature_dim
        features = features[:self.feature_dim]
        while len(features) < self.feature_dim:
            features.append(0.0)
        
        return np.array(features, dtype=np.float32)
    
    def _rating_to_score(self, rating: str) -> float:
        """Convert credit rating to numerical score"""
        rating_map = {
            'AAA': 1.0, 'AA+': 0.95, 'AA': 0.9, 'AA-': 0.85,
            'A+': 0.8, 'A': 0.75, 'A-': 0.7,
            'BBB+': 0.65, 'BBB': 0.6, 'BBB-': 0.55,
            'BB+': 0.5, 'BB': 0.45, 'BB-': 0.4,
            'B+': 0.35, 'B': 0.3, 'B-': 0.25,
            'CCC': 0.2, 'CC': 0.15, 'C': 0.1, 'D': 0.05
        }
        return rating_map.get(rating.upper(), 0.6)  # Default to BBB
    
    async def create_quantum_state(self, bond_data: Dict[str, Any]) -> QuantumBondState:
        """Create quantum state representation for a bond"""
        if not self.is_initialized:
            await self.initialize()
        
        # Extract features
        features = self.extract_bond_features(bond_data)
        
        # Create quantum state vector through network
        with torch.no_grad():
            feature_tensor = torch.tensor(features).unsqueeze(0)
            state_vector, attention = self.quantum_network(feature_tensor)
            state_vector = state_vector.squeeze(0).numpy()
        
        # Create probability amplitude (complex number)
        amplitude_real = np.linalg.norm(state_vector[:self.output_dim//2])
        amplitude_imag = np.linalg.norm(state_vector[self.output_dim//2:])
        probability_amplitude = complex(amplitude_real, amplitude_imag)
        
        # Normalize amplitude
        amplitude_norm = abs(probability_amplitude)
        if amplitude_norm > 0:
            probability_amplitude /= amplitude_norm
        
        quantum_state = QuantumBondState(
            bond_id=bond_data['id'],
            cusip=bond_data['cusip'],
            state_vector=state_vector,
            probability_amplitude=probability_amplitude,
            entanglement_partners=[],
            coherence_time=1.0,
            measurement_history=[]
        )
        
        self.bond_states[bond_data['id']] = quantum_state
        return quantum_state
    
    async def calculate_quantum_similarity(self, bond1_id: str, bond2_id: str) -> float:
        """Calculate quantum similarity between two bonds"""
        if bond1_id not in self.bond_states or bond2_id not in self.bond_states:
            return 0.0
        
        state1 = self.bond_states[bond1_id]
        state2 = self.bond_states[bond2_id]
        
        # Quantum fidelity calculation
        fidelity = abs(np.dot(state1.state_vector, state2.state_vector.conj()))**2
        
        # Apply quantum interference effects
        phase_diff = np.angle(state1.probability_amplitude) - np.angle(state2.probability_amplitude)
        interference = np.cos(phase_diff)
        
        # Combine fidelity and interference
        quantum_similarity = fidelity * (1 + 0.1 * interference)
        
        # Apply coherence decay
        coherence_factor = min(state1.coherence_time, state2.coherence_time)
        quantum_similarity *= coherence_factor
        
        return min(quantum_similarity, 1.0)
    
    async def find_entangled_bonds(self, target_bond_id: str, threshold: float = None) -> List[Tuple[str, float]]:
        """Find bonds entangled with target bond"""
        if threshold is None:
            threshold = self.entanglement_threshold
        
        if target_bond_id not in self.bond_states:
            return []
        
        entangled_bonds = []
        
        for bond_id, bond_state in self.bond_states.items():
            if bond_id == target_bond_id:
                continue
            
            similarity = await self.calculate_quantum_similarity(target_bond_id, bond_id)
            
            if similarity >= threshold:
                entangled_bonds.append((bond_id, similarity))
        
        # Sort by similarity (highest first)
        entangled_bonds.sort(key=lambda x: x[1], reverse=True)
        
        return entangled_bonds
    
    async def quantum_measurement(self, bond_id: str, observable: str) -> Dict[str, Any]:
        """Perform quantum measurement on bond state"""
        if bond_id not in self.bond_states:
            return {}
        
        bond_state = self.bond_states[bond_id]
        
        # Add measurement noise
        noise = np.random.normal(0, self.measurement_noise, bond_state.state_vector.shape)
        measured_state = bond_state.state_vector + noise
        
        # Collapse state (measurement effect)
        collapsed_state = measured_state / np.linalg.norm(measured_state)
        
        # Update bond state
        bond_state.state_vector = collapsed_state
        bond_state.coherence_time *= self.coherence_decay
        
        # Record measurement
        measurement_record = {
            'timestamp': datetime.utcnow().isoformat(),
            'observable': observable,
            'result': float(np.linalg.norm(collapsed_state)),
            'coherence_time': bond_state.coherence_time
        }
        
        bond_state.measurement_history.append(measurement_record)
        
        return measurement_record
    
    async def update_coherence(self):
        """Update quantum coherence for all bond states"""
        for bond_state in self.bond_states.values():
            bond_state.coherence_time *= self.coherence_decay
            
            # Reset coherence if it gets too low
            if bond_state.coherence_time < 0.1:
                bond_state.coherence_time = 1.0
    
    def save_model(self, path: str):
        """Save quantum cognition model"""
        torch.save({
            'model_state_dict': self.quantum_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'feature_dim': self.feature_dim,
            'hidden_dim': self.hidden_dim,
            'output_dim': self.output_dim,
        }, path)
