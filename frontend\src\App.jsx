import React, { Suspense, lazy } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout, Spin } from 'antd'
import { Helmet } from 'react-helmet-async'
import { motion, AnimatePresence } from 'framer-motion'

import { useAuth } from '@services/auth'
import { useWebSocket } from '@services/websocket'
import ProtectedRoute from '@components/Common/ProtectedRoute'
import LoadingSpinner from '@components/Common/LoadingSpinner'
import Sidebar from '@components/Common/Sidebar'
import Header from '@components/Common/Header'
import Footer from '@components/Common/Footer'

// Lazy load pages for code splitting
const Login = lazy(() => import('@pages/Login'))
const Dashboard = lazy(() => import('@pages/Dashboard'))
const Portfolio = lazy(() => import('@pages/Portfolio'))
const BondAnalysis = lazy(() => import('@pages/BondAnalysis'))
const Trading = lazy(() => import('@pages/Trading'))
const Analytics = lazy(() => import('@pages/Analytics'))
const Settings = lazy(() => import('@pages/Settings'))
const NotFound = lazy(() => import('@pages/NotFound'))

const { Content } = Layout

// Page transition variants
const pageVariants = {
  initial: {
    opacity: 0,
    x: -20,
  },
  in: {
    opacity: 1,
    x: 0,
  },
  out: {
    opacity: 0,
    x: 20,
  },
}

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.3,
}

// Loading fallback component
const PageLoadingFallback = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    minHeight: '400px' 
  }}>
    <Spin size="large" tip="Loading page..." />
  </div>
)

// Main App component
function App() {
  const { user, isLoading: authLoading } = useAuth()
  const { isConnected, connectionStatus } = useWebSocket()

  // Show loading spinner while checking authentication
  if (authLoading) {
    return <LoadingSpinner message="Initializing LiquidBond..." />
  }

  // Authenticated app layout
  if (user) {
    return (
      <>
        <Helmet>
          <title>LiquidBond - Dashboard</title>
          <meta name="description" content="AI-Powered Bond Liquidity Optimization Dashboard" />
        </Helmet>
        
        <Layout style={{ minHeight: '100vh' }}>
          <Sidebar />
          
          <Layout>
            <Header 
              user={user} 
              connectionStatus={connectionStatus}
              isConnected={isConnected}
            />
            
            <Content style={{ 
              margin: '24px 16px', 
              padding: '24px',
              background: '#fff',
              borderRadius: '8px',
              minHeight: 'calc(100vh - 112px)'
            }}>
              <AnimatePresence mode="wait">
                <Suspense fallback={<PageLoadingFallback />}>
                  <Routes>
                    <Route 
                      path="/" 
                      element={
                        <motion.div
                          initial="initial"
                          animate="in"
                          exit="out"
                          variants={pageVariants}
                          transition={pageTransition}
                        >
                          <Dashboard />
                        </motion.div>
                      } 
                    />
                    
                    <Route 
                      path="/portfolio" 
                      element={
                        <ProtectedRoute requiredRole="portfolio_manager">
                          <motion.div
                            initial="initial"
                            animate="in"
                            exit="out"
                            variants={pageVariants}
                            transition={pageTransition}
                          >
                            <Portfolio />
                          </motion.div>
                        </ProtectedRoute>
                      } 
                    />
                    
                    <Route 
                      path="/bonds" 
                      element={
                        <motion.div
                          initial="initial"
                          animate="in"
                          exit="out"
                          variants={pageVariants}
                          transition={pageTransition}
                        >
                          <BondAnalysis />
                        </motion.div>
                      } 
                    />
                    
                    <Route 
                      path="/trading" 
                      element={
                        <ProtectedRoute requiredRole="trader">
                          <motion.div
                            initial="initial"
                            animate="in"
                            exit="out"
                            variants={pageVariants}
                            transition={pageTransition}
                          >
                            <Trading />
                          </motion.div>
                        </ProtectedRoute>
                      } 
                    />
                    
                    <Route 
                      path="/analytics" 
                      element={
                        <motion.div
                          initial="initial"
                          animate="in"
                          exit="out"
                          variants={pageVariants}
                          transition={pageTransition}
                        >
                          <Analytics />
                        </motion.div>
                      } 
                    />
                    
                    <Route 
                      path="/settings" 
                      element={
                        <motion.div
                          initial="initial"
                          animate="in"
                          exit="out"
                          variants={pageVariants}
                          transition={pageTransition}
                        >
                          <Settings />
                        </motion.div>
                      } 
                    />
                    
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </Suspense>
              </AnimatePresence>
            </Content>
            
            <Footer />
          </Layout>
        </Layout>
      </>
    )
  }

  // Unauthenticated app (login flow)
  return (
    <>
      <Helmet>
        <title>LiquidBond - Login</title>
        <meta name="description" content="Login to LiquidBond - AI-Powered Bond Liquidity Optimization Platform" />
      </Helmet>
      
      <Suspense fallback={<LoadingSpinner message="Loading login..." />}>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </Suspense>
    </>
  )
}

export default App
